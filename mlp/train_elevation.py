#!/usr/bin/env python3
"""
Quadriga数据集俯仰角估计训练流程
基于原始train.py修改，专门用于俯仰角(elevation)估计
包含数据预处理、模型训练和评估
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import time
import os
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 尝试导入scipy，如果没有则提供替代方案
try:
    from scipy import stats
    SCIPY_AVAILABLE = True
except ImportError:
    print("警告: scipy库未安装，某些统计功能将被禁用")
    SCIPY_AVAILABLE = False

# 假设complexPyTorch库已安装
try:
    from complexPyTorch.complexLayers import ComplexBatchNorm2d, ComplexConv2d, ComplexLinear
    from complexPyTorch.complexFunctions import complex_relu, complex_max_pool2d
except ImportError:
    print("警告: complexPyTorch库未安装，将使用模拟的复数层")
    
    # 模拟复数层定义 (简化版本)
    class ComplexLinear(nn.Module):
        def __init__(self, in_features, out_features):
            super().__init__()
            self.real_linear = nn.Linear(in_features, out_features)
            self.imag_linear = nn.Linear(in_features, out_features)
            
        def forward(self, x):
            real_out = self.real_linear(x.real) - self.imag_linear(x.imag)
            imag_out = self.real_linear(x.imag) + self.imag_linear(x.real)
            return torch.complex(real_out, imag_out)
    
    class ComplexConv2d(nn.Module):
        def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0):
            super().__init__()
            self.real_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
            self.imag_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
            
        def forward(self, x):
            real_out = self.real_conv(x.real) - self.imag_conv(x.imag)
            imag_out = self.real_conv(x.imag) + self.imag_conv(x.real)
            return torch.complex(real_out, imag_out)
    
    class ComplexBatchNorm2d(nn.Module):
        def __init__(self, num_features):
            super().__init__()
            self.real_bn = nn.BatchNorm2d(num_features)
            self.imag_bn = nn.BatchNorm2d(num_features)
            
        def forward(self, x):
            return torch.complex(self.real_bn(x.real), self.imag_bn(x.imag))
    
    def complex_relu(x):
        return torch.complex(torch.relu(x.real), torch.relu(x.imag))

# 模型定义
class Flatten(nn.Module):
    def forward(self, input):
        return input.view(input.size(0), -1)

class HH2ComplexMLPElevation(nn.Module):
    """仰角估计专用复数MLP模型"""
    def __init__(self, input_size, device, n_outputs=1):
        super().__init__()
        self.device = device
        self.n_outputs = n_outputs
        self.input_size = input_size
        
        # 由于输入是实数（实部虚部分离），复数维度是输入的一半
        complex_input_size = input_size // 2
        
        self.flatten = Flatten()
        self.mlp_1 = ComplexLinear(complex_input_size, complex_input_size // 2)
        self.mlp_2 = ComplexLinear(complex_input_size // 2, complex_input_size // 4)
        self.mlp_3 = ComplexLinear(complex_input_size // 4, complex_input_size // 8)
        self.mlp_5 = ComplexLinear(complex_input_size // 8, n_outputs)
        
        print(f"   模型初始化:")
        print(f"     输入尺寸: {input_size}")
        print(f"     复数输入尺寸: {complex_input_size}")
        print(f"     输出尺寸: {n_outputs}")
        
    def forward(self, x):
        # 确保输入是实数张量，然后转换为复数
        if torch.is_complex(x):
            # 如果已经是复数，转换为实数（实部虚部分离）
            x = torch.cat([x.real, x.imag], dim=-1)
        
        # 展平输入
        if len(x.shape) > 2:
            x = x.view(x.size(0), -1)
        
        # 转换为复数（假设实部虚部分离）
        if x.shape[-1] % 2 == 0:
            half_size = x.shape[-1] // 2
            real_part = x[..., :half_size]
            imag_part = x[..., half_size:]
            x = torch.complex(real_part, imag_part)
        else:
            raise ValueError(f"输入维度 {x.shape[-1]} 不是偶数，无法转换为复数")
        
        # 前向传播
        x = self.mlp_1(x)
        x = complex_relu(x)
        x = self.mlp_2(x)
        x = complex_relu(x)
        x = self.mlp_3(x)
        x = complex_relu(x)
        x = self.mlp_5(x)
        
        # 仰角：-90到90度，使用atan2获取相位角度
        output = (90/np.pi) * torch.atan2(x.imag, x.real)
        
        return output

class HH2ComplexMLPJoint(nn.Module):
    """联合估计方位角和俯仰角的复数MLP模型"""
    def __init__(self, input_size, device, n_outputs=2):
        super().__init__()
        self.flatten = Flatten()
        self.mlp_1 = ComplexLinear(input_size, input_size // 2)
        self.mlp_2 = ComplexLinear(input_size // 2, input_size // 4)
        self.mlp_3 = ComplexLinear(input_size // 4, input_size // 8)
        
        # 分别为方位角和俯仰角设计输出层
        self.azimuth_head = ComplexLinear(input_size // 8, 1)
        self.elevation_head = ComplexLinear(input_size // 8, 1)
        
    def forward(self, x):
        x = self.mlp_1(x)
        x = complex_relu(x)
        x = self.mlp_2(x)
        x = complex_relu(x)
        x = self.mlp_3(x)
        x = complex_relu(x)
        
        # 分别计算方位角和俯仰角
        azimuth_complex = self.azimuth_head(x)
        elevation_complex = self.elevation_head(x)
        
        # 方位角：-180到180度
        azimuth = (180/np.pi) * azimuth_complex.angle()
        # 俯仰角：-90到90度
        elevation = (90/np.pi) * elevation_complex.angle()
        
        # 拼接输出
        output = torch.cat([azimuth, elevation], dim=1)
        return output

class HH2ComplexCONVElevation(nn.Module):
    """用于俯仰角估计的复数CNN模型"""
    def __init__(self, input_size, device, n_outputs=1):
        super().__init__()
        self.flatten = Flatten()
        self.conv_1 = ComplexConv2d(1, 1, kernel_size=(3,3), stride=1, padding=2)
        self.conv_2 = ComplexConv2d(1, 1, kernel_size=(3,3), stride=1, padding=2)
        self.bn_1 = ComplexBatchNorm2d(1)
        self.bn_2 = ComplexBatchNorm2d(1)
        self.mlp_1 = ComplexLinear(input_size, input_size // 2)
        self.mlp_2 = ComplexLinear(input_size // 2, input_size // 4)
        self.mlp_3 = ComplexLinear(input_size // 4, input_size // 8)
        self.mlp_4 = ComplexLinear(input_size // 8, n_outputs)
        
    def forward(self, x):
        x = self.conv_1(x)
        x = self.bn_1(x)
        x = complex_relu(x)
        
        x = self.conv_2(x)
        x = self.bn_2(x)
        x = complex_relu(x)
        x = x.view(x.size(0), -1)
        x = self.mlp_1(x)
        x = complex_relu(x)
        x = self.mlp_2(x)
        x = complex_relu(x)
        x = self.mlp_3(x)
        x = complex_relu(x)
        x = self.mlp_4(x)
        # 将复数角度从-π到π映射到-90到90度（俯仰角范围）
        output = (90/np.pi) * x.angle()
        return output

# 数据预处理类（专门针对俯仰角估计优化）
class QuadrigaElevationDataPreprocessor:
    def __init__(self, data_path, H_ants=8, V_ants=4, nSub=17, no_snapshots=1):
        self.data_path = data_path
        self.H_ants = H_ants
        self.V_ants = V_ants
        self.nSub = nSub
        self.no_snapshots = no_snapshots
        self.UE_port = 4  # UE天线端口数
        self.BS_port = H_ants * V_ants * 2  # BS天线端口数
        
    def load_channel_data(self, filename='H_freq.txt'):
        """加载信道数据，保持垂直天线阵列的结构信息"""
        file_path = os.path.join(self.data_path, filename)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"数据文件不存在: {file_path}")
        
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        complex_data_list = []
        
        for line in lines:
            if line.strip():
                values = list(map(float, line.strip().split()))
                values = np.array(values)
                
                # 重塑为实部虚部对
                real_imag_pairs = values.reshape(2, -1)
                complex_values = real_imag_pairs[0] + 1j * real_imag_pairs[1]
                
                # 重塑为信道矩阵，保持天线阵列的空间结构
                if filename == 'H_freq.txt':
                    channel_matrix = complex_values.reshape(self.BS_port, self.UE_port, self.nSub)
                    # 重新组织为 [H_ants, V_ants, 2, UE_port, nSub] 以保持空间结构
                    channel_matrix_structured = channel_matrix.reshape(
                        self.H_ants, self.V_ants, 2, self.UE_port, self.nSub
                    )
                else:
                    channel_matrix = complex_values.reshape(self.BS_port, self.UE_port, self.no_snapshots)
                    channel_matrix_structured = channel_matrix.reshape(
                        self.H_ants, self.V_ants, 2, self.UE_port, self.no_snapshots
                    )
                
                complex_data_list.append(channel_matrix_structured)
        
        return np.array(complex_data_list)
    
    def load_location_data(self, filename='loc.txt'):
        """加载UE位置数据"""
        file_path = os.path.join(self.data_path, filename)
        
        if not os.path.exists(file_path):
            print(f"警告: 位置文件不存在: {file_path}")
            return None
        
        locations = []
        with open(file_path, 'r') as f:
            for line in f:
                if line.strip():
                    coords = list(map(float, line.strip().split()))
                    locations.append(coords[:3])  # x, y, z坐标
        
        return np.array(locations)
    
    def create_elevation_focused_features(self, channel_data):
        """创建专门用于俯仰角估计的特征"""
        N_samples = channel_data.shape[0]
        
        print(f"   原始信道数据形状: {channel_data.shape}")
        
        # 提取垂直维度的信息
        # channel_data shape: [N_samples, H_ants, V_ants, 2, UE_port, nSub]
        
        # 方法1: 沿垂直天线维度计算相关性
        vertical_correlations = []
        for sample_idx in range(N_samples):
            sample_data = channel_data[sample_idx]  # [H_ants, V_ants, 2, UE_port, nSub]
            
            # 计算垂直天线间的相位差
            vertical_features = []
            for h_idx in range(self.H_ants):
                for pol_idx in range(2):  # 极化
                    for ue_idx in range(self.UE_port):
                        for sub_idx in range(self.nSub):
                            # 提取垂直天线阵列的响应
                            vertical_response = sample_data[h_idx, :, pol_idx, ue_idx, sub_idx]
                            # 只保存实部和虚部，而不是复数对象
                            vertical_features.extend(vertical_response.real.tolist())
                            vertical_features.extend(vertical_response.imag.tolist())
            
            vertical_correlations.append(vertical_features)
        
        # 转换为numpy数组
        features_array = np.array(vertical_correlations, dtype=np.float32)
        
        print(f"   仰角特征形状: {features_array.shape}")
        print(f"   特征统计: 均值={features_array.mean():.6f}, 标准差={features_array.std():.6f}")
        
        return features_array
    
    def normalize_data(self, data, method='standardize'):
        """数据归一化"""
        if data is None:
            return None, None
            
        original_shape = data.shape
        
        if np.iscomplexobj(data):
            real_part = data.real.reshape(-1, 1)
            imag_part = data.imag.reshape(-1, 1)
            
            if method == 'standardize':
                scaler_real = StandardScaler()
                scaler_imag = StandardScaler()
            else:
                scaler_real = MinMaxScaler()
                scaler_imag = MinMaxScaler()
            
            real_normalized = scaler_real.fit_transform(real_part)
            imag_normalized = scaler_imag.fit_transform(imag_part)
            
            normalized_data = (real_normalized + 1j * imag_normalized).reshape(original_shape)
            scaler = (scaler_real, scaler_imag)
        else:
            data_reshaped = data.reshape(-1, 1)
            if method == 'standardize':
                scaler = StandardScaler()
            else:
                scaler = MinMaxScaler()
            
            normalized_data = scaler.fit_transform(data_reshaped).reshape(original_shape)
        
        return normalized_data, scaler

class QuadrigaElevationDataset(Dataset):
    """仰角估计专用数据集"""
    def __init__(self, channel_data, labels, locations=None, model_type='MLP', estimation_type='elevation'):
        self.channel_data = torch.FloatTensor(channel_data)
        self.labels = torch.FloatTensor(labels)
        self.locations = torch.FloatTensor(locations) if locations is not None else None
        self.model_type = model_type
        self.estimation_type = estimation_type
        
        print(f"   数据集初始化:")
        print(f"     信道数据形状: {self.channel_data.shape}")
        print(f"     标签形状: {self.labels.shape}")
        print(f"     估计类型: {self.estimation_type}")
        
    def __len__(self):
        return len(self.channel_data)
    
    def __getitem__(self, idx):
        channel = self.channel_data[idx]
        label = self.labels[idx]
        
        # 确保标签是标量
        if len(label.shape) > 0 and label.shape[0] == 1:
            label = label.squeeze()
        
        # 对于MLP，确保输入是1D
        if self.model_type == 'MLP' and len(channel.shape) > 1:
            channel = channel.view(-1)
        
        # 不在这里转换为复数，保持实数格式
        # 让模型的forward方法处理复数转换
        
        # 返回字典格式，与训练器期望的格式匹配
        return {
            'channel': channel,
            'label': label
        }

class QuadrigaElevationTrainer:
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu', estimation_type='elevation'):
        self.model = model.to(device)
        self.device = device
        self.estimation_type = estimation_type
        self.train_losses = []
        self.val_losses = []
        self.val_maes = []
        self.val_stds = []

    def train_epoch(self, train_loader, optimizer, criterion, epoch):
        self.model.train()
        total_loss = 0.0
        num_batches = 0

        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1} Training')

        for batch in progress_bar:
            channel_data = batch['channel'].to(self.device)
            labels = batch['label'].to(self.device)

            optimizer.zero_grad()
            outputs = self.model(channel_data)

            if outputs.dim() > 1 and self.estimation_type != 'joint':
                outputs = outputs.squeeze()

            # 对于联合估计，labels应该是2维的
            if self.estimation_type == 'joint':
                if labels.dim() == 1:
                    # 如果labels是1维的，需要扩展
                    labels = labels.unsqueeze(1).repeat(1, 2)
                loss = criterion(outputs, labels)
            else:
                loss = criterion(outputs, labels)

            loss.backward()

            # 梯度裁剪防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

            optimizer.step()

            total_loss += loss.item()
            num_batches += 1

            progress_bar.set_postfix({'Loss': f'{loss.item():.6f}'})

        return total_loss / num_batches

    def validate_epoch(self, val_loader, criterion):
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        batch_maes = []
        num_batches = 0

        with torch.no_grad():
            for batch in val_loader:
                # 确保正确获取数据
                if isinstance(batch, dict):
                    channel_data = batch['channel'].to(self.device)
                    labels = batch['label'].to(self.device)
                else:
                    # 如果是元组格式
                    channel_data, labels = batch
                    channel_data = channel_data.to(self.device)
                    labels = labels.to(self.device)

                outputs = self.model(channel_data)
                if outputs.dim() > 1 and self.estimation_type != 'joint':
                    outputs = outputs.squeeze()

                # 处理联合估计的情况
                if self.estimation_type == 'joint':
                    if labels.dim() == 1:
                        labels = labels.unsqueeze(1).repeat(1, 2)
                    loss = criterion(outputs, labels)
                else:
                    loss = criterion(outputs, labels)

                total_loss += loss.item()
                num_batches += 1

                # 转换为numpy数组
                batch_predictions = outputs.cpu().numpy()
                batch_labels = labels.cpu().numpy()

                # 计算当前批次的MAE
                if self.estimation_type == 'joint':
                    # 对于联合估计，计算平均MAE
                    batch_mae = np.mean([
                        mean_absolute_error(batch_labels[:, 0], batch_predictions[:, 0]),
                        mean_absolute_error(batch_labels[:, 1], batch_predictions[:, 1])
                    ])
                else:
                    batch_mae = mean_absolute_error(batch_labels, batch_predictions)
                batch_maes.append(batch_mae)

                all_predictions.extend(batch_predictions)
                all_labels.extend(batch_labels)

        # 计算总体指标
        predictions = np.array(all_predictions)
        labels = np.array(all_labels)

        if self.estimation_type == 'joint':
            mae = np.mean([
                mean_absolute_error(labels[:, 0], predictions[:, 0]),
                mean_absolute_error(labels[:, 1], predictions[:, 1])
            ])
            mse = np.mean([
                mean_squared_error(labels[:, 0], predictions[:, 0]),
                mean_squared_error(labels[:, 1], predictions[:, 1])
            ])
        else:
            mae = mean_absolute_error(labels, predictions)
            mse = mean_squared_error(labels, predictions)

        rmse = np.sqrt(mse)
        mae_std = np.std(batch_maes)

        return total_loss / num_batches, mae, rmse, mae_std, predictions, labels

    def train(self, train_loader, val_loader, num_epochs=100, learning_rate=0.001,
              patience=10, save_path='best_elevation_model.pth'):

        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)

        best_mae = float('inf')
        patience_counter = 0

        print(f"开始训练俯仰角估计模型，设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")

        for epoch in range(num_epochs):
            start_time = time.time()

            # 训练
            train_loss = self.train_epoch(train_loader, optimizer, criterion, epoch)

            # 验证
            val_loss, val_mae, val_rmse, mae_std, predictions, labels = self.validate_epoch(val_loader, criterion)

            # 学习率调度
            scheduler.step(val_mae)

            # 记录指标
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            self.val_maes.append(val_mae)
            self.val_stds.append(mae_std)

            epoch_time = time.time() - start_time

            # 打印信息
            print(f'Epoch [{epoch+1:3d}/{num_epochs}] ({epoch_time:.1f}s) | '
                  f'Train: {train_loss:.6f} | Val: {val_loss:.6f} | '
                  f'MAE: {val_mae:.6f} ± {mae_std:.6f} | RMSE: {val_rmse:.6f} | '
                  f'LR: {optimizer.param_groups[0]["lr"]:.1e}')

            # 早停和模型保存
            if val_mae < best_mae:
                best_mae = val_mae
                patience_counter = 0
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'best_mae': best_mae,
                    'train_losses': self.train_losses,
                    'val_losses': self.val_losses,
                    'val_maes': self.val_maes,
                    'val_stds': self.val_stds,
                    'estimation_type': self.estimation_type
                }, save_path)
                print(f'  ✓ 新的最佳MAE! 模型已保存')
            else:
                patience_counter += 1

            if patience_counter >= patience:
                print(f'\n早停触发，已连续{patience}个epoch无改善')
                break

        return best_mae

def preprocess_quadriga_elevation_data(data_path, model_type='MLP', estimation_type='elevation',
                                     use_covariance=False, normalize=True, test_split=0.2, batch_size=32):
    """仰角估计专用数据预处理流程"""

    # 强制确保只处理仰角
    if estimation_type != 'elevation':
        print(f"⚠️  警告: 强制设置estimation_type为'elevation'，当前值'{estimation_type}'被忽略")
        estimation_type = 'elevation'

    print("="*60)
    print("开始Quadriga仰角估计数据预处理 (仅仰角)")
    print("="*60)

    # 初始化预处理器
    preprocessor = QuadrigaElevationDataPreprocessor(data_path)

    try:
        # 加载数据
        print("📁 加载信道数据...")
        channel_data = preprocessor.load_channel_data('H_freq.txt')
        print(f"   ✓ 信道数据形状: {channel_data.shape}")

        print("📍 加载位置数据...")
        locations = preprocessor.load_location_data('loc.txt')
        if locations is not None:
            print(f"   ✓ 位置数据形状: {locations.shape}")

    except FileNotFoundError as e:
        print(f"❌ 文件加载错误: {e}")
        print("使用模拟数据进行演示...")
        return create_dummy_elevation_data(batch_size, 'elevation')

    # 创建专门的仰角特征
    if model_type == 'MLP':
        print("🔄 创建仰角专用特征...")
        channel_features = preprocessor.create_elevation_focused_features(channel_data)
        print(f"   ✓ 仰角特征形状: {channel_features.shape}")
    else:
        channel_features = channel_data.reshape(channel_data.shape[0], -1)

    # 数据归一化
    if normalize:
        print("📏 数据归一化...")
        channel_features, channel_scaler = preprocessor.normalize_data(channel_features)
        locations, loc_scaler = preprocessor.normalize_data(locations, method='minmax') if locations is not None else (None, None)
        print("   ✓ 归一化完成")

    # 创建仰角标签
    print("🏷️  创建仰角训练标签...")
    if locations is not None:
        # 只计算仰角
        r_horizontal = np.sqrt(locations[:, 0]**2 + locations[:, 1]**2)
        elevation_angles = np.arctan2(locations[:, 2], r_horizontal) * 180 / np.pi
        labels = elevation_angles
        print(f"   ✓ 仰角标签范围: [{labels.min():.1f}°, {labels.max():.1f}°]")
        print(f"   ✓ 仰角标签统计: 均值={labels.mean():.1f}°, 标准差={labels.std():.1f}°")
    else:
        # 使用模拟仰角标签
        labels = np.random.uniform(-90, 90, len(channel_features))
        print("   ✓ 使用模拟仰角标签")

    # 数据集分割
    print("✂️  数据集分割...")
    n_samples = len(channel_features)
    indices = np.random.permutation(n_samples)
    split_idx = int(n_samples * (1 - test_split))

    train_indices = indices[:split_idx]
    val_indices = indices[split_idx:]

    print(f"   ✓ 训练集: {len(train_indices)} 样本")
    print(f"   ✓ 验证集: {len(val_indices)} 样本")

    # 创建仰角专用数据集
    train_dataset = QuadrigaElevationDataset(
        channel_features[train_indices],
        labels[train_indices],
        locations[train_indices] if locations is not None else None,
        model_type=model_type,
        estimation_type='elevation'  # 强制仰角
    )

    val_dataset = QuadrigaElevationDataset(
        channel_features[val_indices],
        labels[val_indices],
        locations[val_indices] if locations is not None else None,
        model_type=model_type,
        estimation_type='elevation'  # 强制仰角
    )

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

    # 计算输入尺寸
    input_size = channel_features.shape[1]

    data_info = {
        'input_size': input_size,
        'n_samples': n_samples,
        'n_train': len(train_indices),
        'n_val': len(val_indices),
        'channel_shape': channel_features.shape,
        'label_range': [labels.min(), labels.max()],
        'label_stats': {
            'mean': labels.mean(),
            'std': labels.std(),
            'median': np.median(labels)
        },
        'estimation_type': 'elevation',  # 确保是仰角
        'scalers': {
            'channel': channel_scaler if normalize else None,
            'location': loc_scaler if normalize and locations is not None else None,
        }
    }

    print("✅ 仰角估计数据预处理完成!")
    return train_loader, val_loader, data_info

def create_dummy_elevation_data(batch_size=32, estimation_type='elevation'):
    """创建仰角估计专用模拟数据"""
    
    # 强制确保只生成仰角数据
    if estimation_type != 'elevation':
        print(f"⚠️  强制设置为仰角估计，忽略参数'{estimation_type}'")
        estimation_type = 'elevation'
    
    print("🎭 创建仰角估计专用模拟数据...")

    n_samples = 2000
    H_ants, V_ants = 8, 4
    UE_port = 4
    nSub = 17

    # 创建具有垂直结构的模拟信道数据
    channel_data = (np.random.randn(n_samples, H_ants, V_ants, 2, UE_port, nSub) +
                   1j * np.random.randn(n_samples, H_ants, V_ants, 2, UE_port, nSub)) * 0.1

    # 创建仰角相关的位置和角度数据
    elevation_true = np.random.uniform(-30, 30, n_samples)  # 限制仰角范围
    azimuth_true = np.random.uniform(-180, 180, n_samples)  # 方位角仅用于生成位置

    # 根据角度生成位置
    distance = np.random.uniform(10, 50, n_samples)
    locations = np.column_stack([
        distance * np.cos(elevation_true * np.pi / 180) * np.cos(azimuth_true * np.pi / 180),
        distance * np.cos(elevation_true * np.pi / 180) * np.sin(azimuth_true * np.pi / 180),
        distance * np.sin(elevation_true * np.pi / 180)
    ])

    # 仅使用仰角作为标签
    labels = elevation_true + np.random.normal(0, 1, n_samples)

    print(f"   ✓ 仰角标签范围: [{labels.min():.1f}°, {labels.max():.1f}°]")

    # 展平信道数据用于MLP
    channel_features = channel_data.reshape(n_samples, -1)
    
    # 转换为实数特征（实部虚部分离）
    channel_real = np.concatenate([channel_features.real, channel_features.imag], axis=1)
    
    print(f"   ✓ 信道特征形状: {channel_real.shape}")
    print(f"   ✓ 每个样本的特征维度: {channel_real.shape[1]}")

    # 数据集分割
    train_size = int(0.8 * n_samples)

    train_dataset = QuadrigaElevationDataset(
        channel_real[:train_size],
        labels[:train_size],
        locations[:train_size],
        model_type='MLP',
        estimation_type='elevation'
    )

    val_dataset = QuadrigaElevationDataset(
        channel_real[train_size:],
        labels[train_size:],
        locations[train_size:],
        model_type='MLP',
        estimation_type='elevation'
    )

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

    # 获取实际输入尺寸
    sample_data = train_dataset[0]['channel']
    actual_input_size = sample_data.numel()
    
    print(f"   ✓ 实际输入尺寸: {actual_input_size}")

    data_info = {
        'input_size': actual_input_size,
        'n_samples': n_samples,
        'n_train': train_size,
        'n_val': n_samples - train_size,
        'channel_shape': channel_real.shape,
        'label_range': [labels.min(), labels.max()],
        'estimation_type': 'elevation',
        'scalers': {'channel': None, 'location': None}
    }

    print("✅ 仰角估计模拟数据创建完成!")
    return train_loader, val_loader, data_info

def evaluate_elevation_model(model, test_loader, device, estimation_type='elevation', model_save_path=None):
    """俯仰角模型评估"""
    if model_save_path and os.path.exists(model_save_path):
        checkpoint = torch.load(model_save_path, map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 已加载最佳模型 (训练轮数: {checkpoint['epoch']+1})")

    model.eval()
    all_predictions = []
    all_labels = []
    batch_maes = []

    print("🔍 开始最终评估...")
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            # 确保正确获取数据
            if isinstance(batch, dict):
                channel_data = batch['channel'].to(device)
                labels = batch['label'].to(device)
            else:
                # 如果是元组格式
                channel_data, labels = batch
                channel_data = channel_data.to(device)
                labels = labels.to(device)

            outputs = model(channel_data)
            if outputs.dim() > 1 and estimation_type != 'joint':
                outputs = outputs.squeeze()

            # 转换为numpy数组
            batch_predictions = outputs.cpu().numpy()
            batch_labels = labels.cpu().numpy()

            # 计算当前批次的MAE
            if estimation_type == 'joint':
                batch_mae = np.mean([
                    mean_absolute_error(batch_labels[:, 0], batch_predictions[:, 0]),
                    mean_absolute_error(batch_labels[:, 1], batch_predictions[:, 1])
                ])
            else:
                batch_mae = mean_absolute_error(batch_labels, batch_predictions)
            batch_maes.append(batch_mae)

            all_predictions.extend(batch_predictions)
            all_labels.extend(batch_labels)

    # 转换为numpy数组
    predictions = np.array(all_predictions)
    labels = np.array(all_labels)
    residuals = predictions - labels

    # 计算各种指标
    if estimation_type == 'joint':
        mae = np.mean([
            mean_absolute_error(labels[:, 0], predictions[:, 0]),
            mean_absolute_error(labels[:, 1], predictions[:, 1])
        ])
        mse = np.mean([
            mean_squared_error(labels[:, 0], predictions[:, 0]),
            mean_squared_error(labels[:, 1], predictions[:, 1])
        ])
        mae_azimuth = mean_absolute_error(labels[:, 0], predictions[:, 0])
        mae_elevation = mean_absolute_error(labels[:, 1], predictions[:, 1])
        mse_azimuth = mean_squared_error(labels[:, 0], predictions[:, 0])
        mse_elevation = mean_squared_error(labels[:, 1], predictions[:, 1])
        max_error = max(np.max(np.abs(residuals[:, 0])), np.max(np.abs(residuals[:, 1])))
        std_error = np.mean([np.std(residuals[:, 0]), np.std(residuals[:, 1])])
    else:
        mae = mean_absolute_error(labels, predictions)
        mse = mean_squared_error(labels, predictions)
        max_error = np.max(np.abs(residuals))
        std_error = np.std(residuals)

    rmse = np.sqrt(mse)
    mae_std = np.std(batch_maes) if len(batch_maes) > 1 else 0.0

    # 计算误差分位数
    percentiles = [50, 75, 90, 95, 99]
    if estimation_type == 'joint':
        abs_errors_azimuth = np.abs(residuals[:, 0])
        abs_errors_elevation = np.abs(residuals[:, 1])
        percentile_errors = {
            'azimuth': {p: np.percentile(abs_errors_azimuth, p) for p in percentiles},
            'elevation': {p: np.percentile(abs_errors_elevation, p) for p in percentiles}
        }
    else:
        abs_errors = np.abs(residuals)
        percentile_errors = {p: np.percentile(abs_errors, p) for p in percentiles}

    metrics = {
        'MAE': mae,
        'MAE_STD': mae_std,
        'MSE': mse,
        'RMSE': rmse,
        'Max_Error': max_error,
        'Std_Error': std_error,
        'STD': std_error,  # 添加STD别名
        'Predictions': predictions,
        'Labels': labels,
        'Residuals': residuals,
        'Percentile_Errors': percentile_errors,
        'Estimation_Type': estimation_type
    }

    if estimation_type == 'joint':
        metrics.update({
            'MAE_Azimuth': mae_azimuth,
            'MAE_Elevation': mae_elevation,
            'MSE_Azimuth': mse_azimuth,
            'MSE_Elevation': mse_elevation
        })

    return metrics

def print_elevation_results(metrics):
    """打印俯仰角估计结果"""
    estimation_type = metrics['Estimation_Type']

    print("\n" + "="*70)
    print(f"🎯 {estimation_type.upper()} 估计模型评估结果")
    print("="*70)

    if estimation_type == 'joint':
        print(f"📊 核心指标:")
        print(f"   总体 MAE:                  {metrics['MAE']:.6f} ± {metrics['MAE_STD']:.6f}")
        print(f"   方位角 MAE:                {metrics['MAE_Azimuth']:.6f}")
        print(f"   俯仰角 MAE:                {metrics['MAE_Elevation']:.6f}")
        print(f"   总体 RMSE:                 {metrics['RMSE']:.6f}")
        print(f"   最大误差:                  {metrics['Max_Error']:.6f}")
        print(f"   误差标准差:                {metrics['Std_Error']:.6f}")

        print(f"\n📈 方位角误差分位数:")
        for percentile, error in metrics['Percentile_Errors']['azimuth'].items():
            print(f"   {percentile:2d}% 误差:              {error:.6f}°")

        print(f"\n📈 俯仰角误差分位数:")
        for percentile, error in metrics['Percentile_Errors']['elevation'].items():
            print(f"   {percentile:2d}% 误差:              {error:.6f}°")
    else:
        print(f"📊 核心指标:")
        print(f"   MAE (平均绝对误差):        {metrics['MAE']:.6f} ± {metrics['MAE_STD']:.6f}")
        print(f"   MSE (均方误差):            {metrics['MSE']:.6f}")
        print(f"   RMSE (均方根误差):         {metrics['RMSE']:.6f}")
        print(f"   最大误差:                  {metrics['Max_Error']:.6f}")
        print(f"   误差标准差:                {metrics['Std_Error']:.6f}")

        print(f"\n📈 误差分位数:")
        for percentile, error in metrics['Percentile_Errors'].items():
            print(f"   {percentile:2d}% 误差:              {error:.6f}°")

    print(f"\n📋 数据统计:")
    if estimation_type == 'joint':
        print(f"   方位角预测范围:            [{metrics['Predictions'][:, 0].min():.3f}, {metrics['Predictions'][:, 0].max():.3f}]")
        print(f"   方位角真实范围:            [{metrics['Labels'][:, 0].min():.3f}, {metrics['Labels'][:, 0].max():.3f}]")
        print(f"   俯仰角预测范围:            [{metrics['Predictions'][:, 1].min():.3f}, {metrics['Predictions'][:, 1].max():.3f}]")
        print(f"   俯仰角真实范围:            [{metrics['Labels'][:, 1].min():.3f}, {metrics['Labels'][:, 1].max():.3f}]")
    else:
        print(f"   预测值范围:                [{metrics['Predictions'].min():.3f}, {metrics['Predictions'].max():.3f}]")
        print(f"   真实值范围:                [{metrics['Labels'].min():.3f}, {metrics['Labels'].max():.3f}]")
    print(f"   样本数量:                  {len(metrics['Labels'])}")

    print("="*70)
    print(f"🏆 最终MAE结果: {metrics['MAE']:.6f} ± {metrics['MAE_STD']:.6f}")
    print("="*70)

def plot_elevation_results(trainer, metrics, save_dir='./results_elevation/'):
    """绘制俯仰角估计训练和评估结果"""
    os.makedirs(save_dir, exist_ok=True)
    estimation_type = metrics['Estimation_Type']

    # 1. 训练曲线
    fig, axes = plt.subplots(1, 3, figsize=(15, 4))

    epochs = range(1, len(trainer.train_losses) + 1)

    # 损失曲线
    axes[0].plot(epochs, trainer.train_losses, 'b-', label='Train Loss', linewidth=2)
    axes[0].plot(epochs, trainer.val_losses, 'r-', label='Val Loss', linewidth=2)
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].set_title('Training and Validation Loss')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # MAE曲线（带误差棒）
    if hasattr(trainer, 'val_stds') and len(trainer.val_stds) > 0:
        axes[1].errorbar(epochs, trainer.val_maes, yerr=trainer.val_stds,
                        fmt='g-', label='Val MAE ± STD', linewidth=2,
                        capsize=3, capthick=1, alpha=0.8)
    else:
        axes[1].plot(epochs, trainer.val_maes, 'g-', label='Val MAE', linewidth=2)
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('MAE')
    axes[1].set_title(f'{estimation_type.title()} MAE with Standard Deviation')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    # 最终MAE高亮
    best_epoch = np.argmin(trainer.val_maes) + 1
    best_mae = min(trainer.val_maes)
    axes[1].plot(best_epoch, best_mae, 'ro', markersize=8, label=f'Best MAE: {best_mae:.4f}')
    axes[1].legend()

    # 误差分布
    if estimation_type == 'joint':
        residuals_combined = np.concatenate([
            np.abs(metrics['Residuals'][:, 0]),
            np.abs(metrics['Residuals'][:, 1])
        ])
        axes[2].hist(residuals_combined, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    else:
        axes[2].hist(np.abs(metrics['Residuals']), bins=50, alpha=0.7, color='skyblue', edgecolor='black')

    axes[2].axvline(x=metrics['MAE'], color='red', linestyle='--', linewidth=2, label=f'MAE: {metrics["MAE"]:.4f}')
    axes[2].set_xlabel('Absolute Error (degrees)')
    axes[2].set_ylabel('Frequency')
    axes[2].set_title(f'{estimation_type.title()} Error Distribution')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'{estimation_type}_training_summary.png'), dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 详细评估图
    if estimation_type == 'joint':
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        predictions = metrics['Predictions']
        labels = metrics['Labels']
        residuals = metrics['Residuals']

        # 方位角预测 vs 真实
        axes[0,0].scatter(labels[:, 0], predictions[:, 0], alpha=0.6, s=20, color='blue')
        min_val = min(labels[:, 0].min(), predictions[:, 0].min())
        max_val = max(labels[:, 0].max(), predictions[:, 0].max())
        axes[0,0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        axes[0,0].set_xlabel('True Azimuth (degrees)')
        axes[0,0].set_ylabel('Predicted Azimuth (degrees)')
        axes[0,0].set_title(f'Azimuth: Predictions vs True\nMAE: {metrics["MAE_Azimuth"]:.4f}°')
        axes[0,0].grid(True, alpha=0.3)

        # 俯仰角预测 vs 真实
        axes[0,1].scatter(labels[:, 1], predictions[:, 1], alpha=0.6, s=20, color='green')
        min_val = min(labels[:, 1].min(), predictions[:, 1].min())
        max_val = max(labels[:, 1].max(), predictions[:, 1].max())
        axes[0,1].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        axes[0,1].set_xlabel('True Elevation (degrees)')
        axes[0,1].set_ylabel('Predicted Elevation (degrees)')
        axes[0,1].set_title(f'Elevation: Predictions vs True\nMAE: {metrics["MAE_Elevation"]:.4f}°')
        axes[0,1].grid(True, alpha=0.3)

        # 联合误差分布
        axes[0,2].hist(np.abs(residuals[:, 0]), bins=30, alpha=0.7, color='blue', label='Azimuth', density=True)
        axes[0,2].hist(np.abs(residuals[:, 1]), bins=30, alpha=0.7, color='green', label='Elevation', density=True)
        axes[0,2].set_xlabel('Absolute Error (degrees)')
        axes[0,2].set_ylabel('Density')
        axes[0,2].set_title('Joint Error Distribution')
        axes[0,2].legend()
        axes[0,2].grid(True, alpha=0.3)

        # 方位角残差散点图
        axes[1,0].scatter(predictions[:, 0], residuals[:, 0], alpha=0.6, s=20, color='blue')
        axes[1,0].axhline(y=0, color='red', linestyle='--', linewidth=2)
        axes[1,0].set_xlabel('Predicted Azimuth (degrees)')
        axes[1,0].set_ylabel('Azimuth Residuals (degrees)')
        axes[1,0].set_title('Azimuth Residual Plot')
        axes[1,0].grid(True, alpha=0.3)

        # 俯仰角残差散点图
        axes[1,1].scatter(predictions[:, 1], residuals[:, 1], alpha=0.6, s=20, color='green')
        axes[1,1].axhline(y=0, color='red', linestyle='--', linewidth=2)
        axes[1,1].set_xlabel('Predicted Elevation (degrees)')
        axes[1,1].set_ylabel('Elevation Residuals (degrees)')
        axes[1,1].set_title('Elevation Residual Plot')
        axes[1,1].grid(True, alpha=0.3)

        # 误差CDF比较
        sorted_errors_az = np.sort(np.abs(residuals[:, 0]))
        sorted_errors_el = np.sort(np.abs(residuals[:, 1]))
        cdf_y = np.arange(1, len(sorted_errors_az) + 1) / len(sorted_errors_az)
        axes[1,2].plot(sorted_errors_az, cdf_y, linewidth=2, color='blue', label='Azimuth')
        axes[1,2].plot(sorted_errors_el, cdf_y, linewidth=2, color='green', label='Elevation')
        axes[1,2].set_xlabel('Absolute Error (degrees)')
        axes[1,2].set_ylabel('Cumulative Probability')
        axes[1,2].set_title('Cumulative Error Distribution')
        axes[1,2].legend()
        axes[1,2].grid(True, alpha=0.3)

    else:
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))

        predictions = metrics['Predictions']
        labels = metrics['Labels']
        residuals = metrics['Residuals']

        # 预测 vs 真实
        axes[0,0].scatter(labels, predictions, alpha=0.6, s=20, color='blue')
        min_val = min(labels.min(), predictions.min())
        max_val = max(labels.max(), predictions.max())
        axes[0,0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        axes[0,0].set_xlabel(f'True {estimation_type.title()} (degrees)')
        axes[0,0].set_ylabel(f'Predicted {estimation_type.title()} (degrees)')
        axes[0,0].set_title(f'{estimation_type.title()}: Predictions vs True\nMAE: {metrics["MAE"]:.4f}°')
        axes[0,0].grid(True, alpha=0.3)

        # 残差散点图
        axes[0,1].scatter(predictions, residuals, alpha=0.6, s=20, color='green')
        axes[0,1].axhline(y=0, color='red', linestyle='--', linewidth=2)
        axes[0,1].set_xlabel(f'Predicted {estimation_type.title()} (degrees)')
        axes[0,1].set_ylabel('Residuals (degrees)')
        axes[0,1].set_title(f'{estimation_type.title()} Residual Plot')
        axes[0,1].grid(True, alpha=0.3)

        # 误差CDF
        sorted_errors = np.sort(np.abs(residuals))
        cdf_y = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors)
        axes[1,0].plot(sorted_errors, cdf_y, linewidth=2, color='purple')
        axes[1,0].axvline(x=metrics['MAE'], color='red', linestyle='--', linewidth=2, label=f'MAE: {metrics["MAE"]:.4f}')
        axes[1,0].set_xlabel('Absolute Error (degrees)')
        axes[1,0].set_ylabel('Cumulative Probability')
        axes[1,0].set_title('Cumulative Error Distribution')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)

        # 误差时间序列
        axes[1,1].plot(np.abs(residuals), alpha=0.7, linewidth=1, color='orange')
        axes[1,1].axhline(y=metrics['MAE'], color='red', linestyle='--', linewidth=2, label=f'MAE: {metrics["MAE"]:.4f}')
        axes[1,1].set_xlabel('Sample Index')
        axes[1,1].set_ylabel('Absolute Error (degrees)')
        axes[1,1].set_title(f'{estimation_type.title()} Error Time Series')
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'{estimation_type}_detailed_evaluation.png'), dpi=300, bbox_inches='tight')
    plt.show()

    print(f"📊 图表已保存到: {save_dir}")

def main():
    """主函数 - 专门用于仰角估计"""
    print("🚀 Quadriga数据集仰角估计训练开始")
    print("="*70)

    # 配置参数 - 确保只进行仰角估计
    config = {
        'data_path': '/home/<USER>/quadriga/uma_v3/',  # 数据路径
        'model_type': 'MLP',  # 'MLP', 'CNN'
        'estimation_type': 'elevation',  # 固定为仰角估计
        'batch_size': 32,
        'num_epochs': 100,
        'learning_rate': 0.001,
        'patience': 20,
        'test_split': 0.2,
        'normalize': True,
        'use_covariance': False,
        'save_dir': './results_elevation_only/',
        'model_save_path': './best_elevation_only_model.pth'
    }

    # 验证配置
    assert config['estimation_type'] == 'elevation', "此脚本仅支持仰角估计"
    
    print("⚙️  仰角估计专用配置:")
    for key, value in config.items():
        print(f"   {key}: {value}")
    print()

    # 设备选择
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"🖥️  使用设备: {device}")
    if device == 'cuda':
        print(f"   GPU: {torch.cuda.get_device_name(0)}")
        print(f"   显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    print()

    try:
        # 数据预处理
        train_loader, val_loader, data_info = preprocess_quadriga_elevation_data(
            data_path=config['data_path'],
            model_type=config['model_type'],
            estimation_type=config['estimation_type'],
            use_covariance=config['use_covariance'],
            normalize=config['normalize'],
            test_split=config['test_split'],
            batch_size=config['batch_size']
        )

        print(f"\n📋 数据信息:")
        print(f"   输入尺寸: {data_info['input_size']}")
        print(f"   训练样本: {data_info['n_train']}")
        print(f"   验证样本: {data_info['n_val']}")
        print(f"   估计类型: {data_info['estimation_type']}")
        if config['estimation_type'] == 'joint':
            print(f"   方位角范围: [{data_info['label_range'][0][0]:.1f}°, {data_info['label_range'][0][1]:.1f}°]")
            print(f"   俯仰角范围: [{data_info['label_range'][1][0]:.1f}°, {data_info['label_range'][1][1]:.1f}°]")
        else:
            print(f"   标签范围: [{data_info['label_range'][0]:.1f}°, {data_info['label_range'][1]:.1f}°]")

    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        print("使用模拟数据继续训练...")
        train_loader, val_loader, data_info = create_dummy_elevation_data(
            config['batch_size'], config['estimation_type']
        )

    # 模型初始化
    print(f"\n🏗️  初始化{config['model_type']}模型...")

    if config['model_type'] == 'MLP':
        if config['estimation_type'] == 'elevation':
            model = HH2ComplexMLPElevation(
                input_size=data_info['input_size'],
                device=device,
                n_outputs=1
            )
        elif config['estimation_type'] == 'azimuth':
            # 使用类似俯仰角的模型，但输出范围是方位角
            class HH2ComplexMLPAzimuth(nn.Module):
                def __init__(self, input_size, device, n_outputs=1):
                    super().__init__()
                    self.flatten = Flatten()
                    self.mlp_1 = ComplexLinear(input_size, input_size // 2)
                    self.mlp_2 = ComplexLinear(input_size // 2, input_size // 4)
                    self.mlp_3 = ComplexLinear(input_size // 4, input_size // 8)
                    self.mlp_5 = ComplexLinear(input_size // 8, n_outputs)

                def forward(self, x):
                    x = self.mlp_1(x)
                    x = complex_relu(x)
                    x = self.mlp_2(x)
                    x = complex_relu(x)
                    x = self.mlp_3(x)
                    x = complex_relu(x)
                    x = self.mlp_5(x)
                    # 方位角：-180到180度
                    output = (180/np.pi) * x.angle()
                    return output

            model = HH2ComplexMLPAzimuth(
                input_size=data_info['input_size'],
                device=device,
                n_outputs=1
            )
        elif config['estimation_type'] == 'joint':
            model = HH2ComplexMLPJoint(
                input_size=data_info['input_size'],
                device=device,
                n_outputs=2
            )
    elif config['model_type'] == 'CNN':
        if config['estimation_type'] in ['elevation', 'azimuth']:
            model = HH2ComplexCONVElevation(
                input_size=data_info['input_size'],
                device=device,
                n_outputs=1
            )
        elif config['estimation_type'] == 'joint':
            # 需要实现联合CNN模型
            print("❌ 联合估计的CNN模型尚未实现")
            return
    else:
        raise ValueError(f"不支持的模型类型: {config['model_type']}")

    print(f"   ✓ 模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 训练器初始化
    trainer = QuadrigaElevationTrainer(model, device, config['estimation_type'])

    # 开始训练
    print(f"\n🎯 开始训练...")
    best_mae = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=config['num_epochs'],
        learning_rate=config['learning_rate'],
        patience=config['patience'],
        save_path=config['model_save_path']
    )

    print(f"\n✅ 训练完成! 最佳MAE: {best_mae:.6f}")

    # 最终评估
    print(f"\n🔍 进行最终评估...")
    metrics = evaluate_elevation_model(
        model=model,
        test_loader=val_loader,
        device=device,
        estimation_type=config['estimation_type'],
        model_save_path=config['model_save_path']
    )

    # 打印结果
    print_elevation_results(metrics)

    # 绘制结果
    print(f"\n📊 生成结果图表...")
    os.makedirs(config['save_dir'], exist_ok=True)
    plot_elevation_results(trainer, metrics, config['save_dir'])

    # 保存详细结果
    results_file = os.path.join(config['save_dir'], f'{config["estimation_type"]}_training_results.npz')
    np.savez(results_file,
             predictions=metrics['Predictions'],
             labels=metrics['Labels'],
             residuals=metrics['Residuals'],
             train_losses=trainer.train_losses,
             val_losses=trainer.val_losses,
             val_maes=trainer.val_maes,
             config=config,
             metrics=metrics)

    print(f"💾 详细结果已保存到: {results_file}")
    print(f"\n🎉 所有任务完成!")

    return metrics

if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)

    print("🎯 仰角估计专用训练脚本")
    print("📋 此脚本仅进行仰角估计，不包含方位角估计")
    print()

    # 运行主程序
    try:
        final_metrics = main()
        print(f"\n🏆 最终仰角估计MAE: {final_metrics['MAE']:.6f}°")
        print(f"🏆 最终仰角估计RMSE: {final_metrics['RMSE']:.6f}°")
        print(f"🏆 最终仰角估计误差标准差: {final_metrics['Std_Error']:.6f}°")
        print(f"🏆 最终仰角估计MAE标准差: {final_metrics['MAE_STD']:.6f}°")
    except KeyboardInterrupt:
        print(f"\n⚠️  仰角估计训练被用户中断")
    except Exception as e:
        print(f"\n❌ 仰角估计训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
"""
俯仰角估计的简化使用示例
演示如何使用train_elevation.py进行不同类型的角度估计
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train_elevation import *

def run_elevation_estimation():
    """运行俯仰角估计示例"""
    print("🎯 俯仰角估计示例")
    print("="*50)
    
    # 简化配置
    config = {
        'model_type': 'MLP',
        'estimation_type': 'elevation',  # 只估计俯仰角
        'batch_size': 16,
        'num_epochs': 20,
        'learning_rate': 0.001,
        'patience': 10,
        'test_split': 0.2,
    }
    
    # 使用模拟数据
    train_loader, val_loader, data_info = create_dummy_elevation_data(
        batch_size=config['batch_size'], 
        estimation_type=config['estimation_type']
    )
    
    # 创建模型
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    model = HH2ComplexMLPElevation(
        input_size=data_info['input_size'],
        device=device,
        n_outputs=1
    )
    
    # 训练
    trainer = QuadrigaElevationTrainer(model, device, config['estimation_type'])
    best_mae = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=config['num_epochs'],
        learning_rate=config['learning_rate'],
        patience=config['patience'],
        save_path='./elevation_demo_model.pth'
    )
    
    # 评估
    metrics = evaluate_elevation_model(
        model=model,
        test_loader=val_loader,
        device=device,
        estimation_type=config['estimation_type'],
        model_save_path='./elevation_demo_model.pth'
    )
    
    print_elevation_results(metrics)
    return metrics

def run_joint_estimation():
    """运行联合估计示例"""
    print("🎯 联合角度估计示例")
    print("="*50)
    
    # 简化配置
    config = {
        'model_type': 'MLP',
        'estimation_type': 'joint',  # 联合估计方位角和俯仰角
        'batch_size': 16,
        'num_epochs': 20,
        'learning_rate': 0.001,
        'patience': 10,
        'test_split': 0.2,
    }
    
    # 使用模拟数据
    train_loader, val_loader, data_info = create_dummy_elevation_data(
        batch_size=config['batch_size'], 
        estimation_type=config['estimation_type']
    )
    
    # 创建模型
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    model = HH2ComplexMLPJoint(
        input_size=data_info['input_size'],
        device=device,
        n_outputs=2
    )
    
    # 训练
    trainer = QuadrigaElevationTrainer(model, device, config['estimation_type'])
    best_mae = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=config['num_epochs'],
        learning_rate=config['learning_rate'],
        patience=config['patience'],
        save_path='./joint_demo_model.pth'
    )
    
    # 评估
    metrics = evaluate_elevation_model(
        model=model,
        test_loader=val_loader,
        device=device,
        estimation_type=config['estimation_type'],
        model_save_path='./joint_demo_model.pth'
    )
    
    print_elevation_results(metrics)
    return metrics

def compare_estimation_types():
    """比较不同估计类型的性能"""
    print("🔍 比较不同估计类型的性能")
    print("="*60)
    
    results = {}
    
    # 测试俯仰角估计
    print("\n1. 俯仰角估计:")
    elevation_metrics = run_elevation_estimation()
    results['elevation'] = elevation_metrics['MAE']
    
    # 测试联合估计
    print("\n2. 联合估计:")
    joint_metrics = run_joint_estimation()
    results['joint'] = joint_metrics['MAE']
    
    # 比较结果
    print("\n📊 性能比较:")
    print("="*40)
    for est_type, mae in results.items():
        print(f"{est_type:12s}: MAE = {mae:.6f}°")
    
    return results

if __name__ == "__main__":
    print("🚀 俯仰角估计演示程序")
    print("="*60)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        # 选择运行模式
        mode = input("选择运行模式 (1: 俯仰角估计, 2: 联合估计, 3: 性能比较): ").strip()
        
        if mode == '1':
            run_elevation_estimation()
        elif mode == '2':
            run_joint_estimation()
        elif mode == '3':
            compare_estimation_types()
        else:
            print("默认运行俯仰角估计示例...")
            run_elevation_estimation()
            
    except KeyboardInterrupt:
        print("\n⚠️  程序被用户中断")
    except Exception as e:
        print(f"\n❌ 运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

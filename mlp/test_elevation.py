#!/usr/bin/env python3
"""
俯仰角估计代码的快速测试脚本
验证模型和数据处理是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import torch
from train_elevation import *

def test_models():
    """测试所有模型是否能正常创建和前向传播"""
    print("🧪 测试模型创建和前向传播")
    print("="*50)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    input_size = 1000  # 模拟输入尺寸
    batch_size = 4
    
    # 测试数据
    test_input = torch.randn(batch_size, input_size, dtype=torch.complex64).to(device)
    
    models_to_test = [
        ("俯仰角MLP", HH2ComplexMLPElevation(input_size, device, 1)),
        ("联合估计MLP", HH2ComplexMLPJoint(input_size, device, 2)),
    ]
    
    for model_name, model in models_to_test:
        try:
            print(f"测试 {model_name}...")
            output = model(test_input)
            print(f"  ✓ 输出形状: {output.shape}")
            print(f"  ✓ 输出范围: [{output.min().item():.2f}, {output.max().item():.2f}]")
            print(f"  ✓ 参数数量: {sum(p.numel() for p in model.parameters()):,}")
        except Exception as e:
            print(f"  ❌ 错误: {e}")
        print()

def test_data_preprocessing():
    """测试数据预处理功能"""
    print("🧪 测试数据预处理")
    print("="*50)
    
    # 测试不同估计类型的数据生成
    estimation_types = ['elevation', 'azimuth', 'joint']
    
    for est_type in estimation_types:
        try:
            print(f"测试 {est_type} 数据生成...")
            train_loader, val_loader, data_info = create_dummy_elevation_data(
                batch_size=8, estimation_type=est_type
            )
            
            # 检查数据加载器
            sample_batch = next(iter(train_loader))
            channel_data = sample_batch['channel']
            labels = sample_batch['label']
            
            print(f"  ✓ 信道数据形状: {channel_data.shape}")
            print(f"  ✓ 标签形状: {labels.shape}")
            print(f"  ✓ 训练样本数: {data_info['n_train']}")
            print(f"  ✓ 验证样本数: {data_info['n_val']}")
            
            if est_type == 'joint':
                print(f"  ✓ 方位角范围: [{labels[:, 0].min():.1f}, {labels[:, 0].max():.1f}]")
                print(f"  ✓ 俯仰角范围: [{labels[:, 1].min():.1f}, {labels[:, 1].max():.1f}]")
            else:
                print(f"  ✓ 标签范围: [{labels.min():.1f}, {labels.max():.1f}]")
                
        except Exception as e:
            print(f"  ❌ 错误: {e}")
        print()

def test_training_loop():
    """测试训练循环是否正常工作"""
    print("🧪 测试训练循环")
    print("="*50)
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # 创建小规模测试数据
    train_loader, val_loader, data_info = create_dummy_elevation_data(
        batch_size=4, estimation_type='elevation'
    )
    
    # 创建模型
    model = HH2ComplexMLPElevation(
        input_size=data_info['input_size'],
        device=device,
        n_outputs=1
    )
    
    # 创建训练器
    trainer = QuadrigaElevationTrainer(model, device, 'elevation')
    
    try:
        print("开始短期训练测试...")
        best_mae = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=3,  # 只训练3个epoch
            learning_rate=0.001,
            patience=10,
            save_path='./test_model.pth'
        )
        
        print(f"  ✓ 训练完成，最佳MAE: {best_mae:.6f}")
        
        # 测试评估
        metrics = evaluate_elevation_model(
            model=model,
            test_loader=val_loader,
            device=device,
            estimation_type='elevation',
            model_save_path='./test_model.pth'
        )
        
        print(f"  ✓ 评估完成，MAE: {metrics['MAE']:.6f}")
        
        # 清理测试文件
        if os.path.exists('./test_model.pth'):
            os.remove('./test_model.pth')
            
    except Exception as e:
        print(f"  ❌ 训练测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_angle_calculations():
    """测试角度计算的正确性"""
    print("🧪 测试角度计算")
    print("="*50)
    
    # 创建测试位置
    test_positions = np.array([
        [1, 0, 0],      # 正x轴，方位角=0°，俯仰角=0°
        [0, 1, 0],      # 正y轴，方位角=90°，俯仰角=0°
        [-1, 0, 0],     # 负x轴，方位角=180°，俯仰角=0°
        [0, -1, 0],     # 负y轴，方位角=-90°，俯仰角=0°
        [1, 0, 1],      # 方位角=0°，俯仰角=45°
        [0, 0, 1],      # 正z轴，俯仰角=90°
    ])
    
    print("测试角度计算:")
    for i, pos in enumerate(test_positions):
        x, y, z = pos
        
        # 计算方位角
        azimuth = np.arctan2(y, x) * 180 / np.pi
        
        # 计算俯仰角
        r_horizontal = np.sqrt(x**2 + y**2)
        elevation = np.arctan2(z, r_horizontal) * 180 / np.pi
        
        print(f"  位置 {i+1}: ({x:2.0f}, {y:2.0f}, {z:2.0f}) -> "
              f"方位角: {azimuth:6.1f}°, 俯仰角: {elevation:6.1f}°")

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始俯仰角估计代码测试")
    print("="*60)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    tests = [
        test_angle_calculations,
        test_models,
        test_data_preprocessing,
        test_training_loop,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
            print("✅ 测试通过\n")
        except Exception as e:
            print(f"❌ 测试失败: {e}\n")
            import traceback
            traceback.print_exc()
            print()
    
    print("="*60)
    print(f"🏆 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！代码可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查代码。")

if __name__ == "__main__":
    run_all_tests()

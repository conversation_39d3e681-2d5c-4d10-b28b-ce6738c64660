# 俯仰角估计训练代码

基于原始 `train.py` 修改的俯仰角估计代码，支持单独的俯仰角估计、方位角估计以及联合估计。

## 主要改进

### 1. 模型架构改进
- **HH2ComplexMLPElevation**: 专门用于俯仰角估计的MLP模型
- **HH2ComplexMLPJoint**: 联合估计方位角和俯仰角的MLP模型  
- **HH2ComplexCONVElevation**: 用于俯仰角估计的CNN模型

### 2. 数据处理改进
- **保持天线阵列空间结构**: 重新组织信道数据为 `[H_ants, V_ants, 2, UE_port, nSub]` 格式
- **俯仰角特征提取**: 专门提取垂直天线阵列的相位信息
- **多类型标签生成**: 支持俯仰角、方位角和联合标签

### 3. 训练流程优化
- **多估计类型支持**: 'elevation', 'azimuth', 'joint'
- **自适应损失函数**: 根据估计类型自动调整
- **改进的评估指标**: 针对不同估计类型的专门评估

## 文件结构

```
mlp/
├── train_elevation.py      # 主要的俯仰角估计训练代码
├── example_elevation.py    # 使用示例和演示
└── README_elevation.md     # 本说明文件
```

## 使用方法

### 1. 基本使用

```python
# 直接运行主程序
python train_elevation.py
```

### 2. 配置参数

在 `main()` 函数中修改配置：

```python
config = {
    'data_path': '/path/to/your/data/',
    'model_type': 'MLP',           # 'MLP' 或 'CNN'
    'estimation_type': 'elevation', # 'elevation', 'azimuth', 'joint'
    'batch_size': 32,
    'num_epochs': 100,
    'learning_rate': 0.0001,
    'patience': 50,
    'test_split': 0.01,
    'normalize': True,
}
```

### 3. 估计类型说明

- **'elevation'**: 只估计俯仰角 (-90° 到 90°)
- **'azimuth'**: 只估计方位角 (-180° 到 180°)  
- **'joint'**: 联合估计方位角和俯仰角

### 4. 使用示例

```python
# 运行演示程序
python example_elevation.py

# 选择运行模式:
# 1: 俯仰角估计
# 2: 联合估计  
# 3: 性能比较
```

## 关键技术改进

### 1. 俯仰角标签计算

```python
# 原始代码只计算方位角
angles = np.arctan2(locations[:, 1], locations[:, 0]) * 180 / np.pi

# 新代码计算俯仰角
r_horizontal = np.sqrt(locations[:, 0]**2 + locations[:, 1]**2)
elevation_angles = np.arctan2(locations[:, 2], r_horizontal) * 180 / np.pi
```

### 2. 天线阵列结构保持

```python
# 重新组织信道数据以保持空间结构
channel_matrix_structured = channel_matrix.reshape(
    self.H_ants, self.V_ants, 2, self.UE_port, self.nSub
)
```

### 3. 联合估计模型

```python
class HH2ComplexMLPJoint(nn.Module):
    def __init__(self, input_size, device, n_outputs=2):
        # 分别为方位角和俯仰角设计输出层
        self.azimuth_head = ComplexLinear(input_size // 8, 1)
        self.elevation_head = ComplexLinear(input_size // 8, 1)
        
    def forward(self, x):
        # 分别计算方位角和俯仰角
        azimuth = (180/np.pi) * azimuth_complex.angle()
        elevation = (90/np.pi) * elevation_complex.angle()
        return torch.cat([azimuth, elevation], dim=1)
```

## 输出结果

### 1. 训练过程
- 实时显示训练损失、验证损失、MAE等指标
- 自动保存最佳模型
- 支持早停机制

### 2. 评估结果
- 详细的误差统计（MAE, RMSE, 分位数误差等）
- 针对联合估计的分别统计
- 可视化结果图表

### 3. 保存文件
- 最佳模型权重 (`.pth`)
- 训练结果数据 (`.npz`)
- 结果图表 (`.png`)

## 性能对比

与原始代码相比的主要优势：

1. **支持俯仰角估计**: 原始代码只能估计方位角
2. **更好的特征提取**: 利用垂直天线阵列的空间信息
3. **联合估计能力**: 可以同时估计方位角和俯仰角
4. **改进的数据处理**: 保持天线阵列的空间结构
5. **更全面的评估**: 针对不同估计类型的专门评估指标

## 注意事项

1. **数据要求**: 需要包含3D位置信息的数据集
2. **天线配置**: 需要垂直天线阵列来感知俯仰角
3. **计算资源**: 联合估计需要更多的计算资源
4. **模型复杂度**: 联合估计模型参数更多，训练时间更长

## 依赖库

```bash
pip install torch torchvision
pip install numpy matplotlib scikit-learn
pip install tqdm
pip install complexPyTorch  # 如果可用
```

## 故障排除

1. **complexPyTorch库未安装**: 代码会自动使用内置的模拟复数层
2. **数据文件不存在**: 会自动切换到模拟数据模式
3. **GPU内存不足**: 可以减小batch_size或使用CPU训练
4. **收敛问题**: 可以调整学习率或增加训练轮数

#!/usr/bin/env python3
"""
双角度估计训练器模块
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from sklearn.metrics import mean_absolute_error, mean_squared_error
import time
from tqdm import tqdm


class QuadrigaTrainerDual:
    """支持多输出的训练器"""
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu', 
                 output_type='dual', loss_weights=None):
        self.model = model.to(device)
        self.device = device
        self.output_type = output_type
        
        # 损失权重：[azimuth_weight, elevation_weight]
        self.loss_weights = loss_weights if loss_weights else [1.0, 1.0]
        
        # 训练记录
        self.train_losses = []
        self.val_losses = []
        self.val_metrics = {
            'azimuth_mae': [],
            'elevation_mae': [],
            'combined_mae': [],
            'azimuth_std': [],
            'elevation_std': [],
            'combined_std': []
        }
        
    def compute_loss(self, outputs, azimuth_targets, elevation_targets):
        """计算损失函数"""
        total_loss = 0.0
        
        if self.output_type == 'dual':
            azimuth_pred = outputs[:, 0]
            elevation_pred = outputs[:, 1]
            
            azimuth_loss = nn.MSELoss()(azimuth_pred, azimuth_targets)
            elevation_loss = nn.MSELoss()(elevation_pred, elevation_targets)
            
            total_loss = (self.loss_weights[0] * azimuth_loss + 
                         self.loss_weights[1] * elevation_loss)
                         
        elif self.output_type == 'azimuth':
            total_loss = nn.MSELoss()(outputs.squeeze(), azimuth_targets)
        elif self.output_type == 'elevation':
            total_loss = nn.MSELoss()(outputs.squeeze(), elevation_targets)
            
        return total_loss
    
    def compute_adaptive_loss(self, outputs, azimuth_targets, elevation_targets, epoch):
        """自适应损失权重计算"""
        if self.output_type == 'dual':
            azimuth_pred = outputs[:, 0]
            elevation_pred = outputs[:, 1]
            
            # 计算当前批次的误差
            az_error = torch.abs(azimuth_pred - azimuth_targets).mean()
            el_error = torch.abs(elevation_pred - elevation_targets).mean()
            
            # 动态调整权重 - 误差大的角度获得更高权重
            total_error = az_error + el_error
            az_weight = (el_error / total_error) * 2  # 归一化到总和为2
            el_weight = (az_error / total_error) * 2
            
            azimuth_loss = nn.MSELoss()(azimuth_pred, azimuth_targets)
            elevation_loss = nn.MSELoss()(elevation_pred, elevation_targets)
            
            return az_weight * azimuth_loss + el_weight * elevation_loss
    
    def compute_balanced_loss(self, outputs, azimuth_targets, elevation_targets):
        """平衡的损失函数"""
        if self.output_type == 'dual':
            azimuth_pred = outputs[:, 0]
            elevation_pred = outputs[:, 1]
            
            # 计算相对误差而不是绝对误差
            az_rel_error = torch.abs(azimuth_pred - azimuth_targets) / 180.0  # 归一化到[0,1]
            el_rel_error = torch.abs(elevation_pred - elevation_targets) / 90.0   # 归一化到[0,1]
            
            # 使用Huber损失减少异常值影响
            az_loss = F.huber_loss(azimuth_pred/180.0, azimuth_targets/180.0)
            el_loss = F.huber_loss(elevation_pred/90.0, elevation_targets/90.0)
            
            return az_loss + el_loss
    
    def train_epoch(self, train_loader, optimizer, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch+1} Training')
        
        for batch in progress_bar:
            channel_data = batch['channel'].to(self.device)
            azimuth_targets = batch.get('azimuth', None)
            elevation_targets = batch.get('elevation', None)
            
            if azimuth_targets is not None:
                azimuth_targets = azimuth_targets.to(self.device)
            if elevation_targets is not None:
                elevation_targets = elevation_targets.to(self.device)
            
            optimizer.zero_grad()
            outputs = self.model(channel_data)
            
            loss = self.compute_loss(outputs, azimuth_targets, elevation_targets)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            progress_bar.set_postfix({'Loss': f'{loss.item():.6f}'})
        
        return total_loss / num_batches
    
    def validate_epoch(self, val_loader):
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0
        azimuth_predictions = []
        elevation_predictions = []
        azimuth_labels = []
        elevation_labels = []
        
        # 存储每个批次的MAE用于计算标准差
        batch_azimuth_maes = []
        batch_elevation_maes = []
        batch_combined_maes = []
        
        with torch.no_grad():
            for batch in val_loader:
                channel_data = batch['channel'].to(self.device)
                azimuth_targets = batch.get('azimuth', None)
                elevation_targets = batch.get('elevation', None)

                outputs = self.model(channel_data)
                
                # 计算损失
                loss = 0
                if self.output_type == 'dual':
                    if azimuth_targets is not None:
                        azimuth_loss = F.mse_loss(outputs[:, 0], azimuth_targets.to(self.device))
                        loss += self.loss_weights[0] * azimuth_loss
                    if elevation_targets is not None:
                        elevation_loss = F.mse_loss(outputs[:, 1], elevation_targets.to(self.device))
                        loss += self.loss_weights[1] * elevation_loss
                
                total_loss += loss.item()
                
                # 收集预测和标签
                if self.output_type == 'dual':
                    batch_azimuth_pred = outputs[:, 0].cpu().numpy()
                    batch_elevation_pred = outputs[:, 1].cpu().numpy()
                    azimuth_predictions.extend(batch_azimuth_pred)
                    elevation_predictions.extend(batch_elevation_pred)
                    
                    if azimuth_targets is not None:
                        batch_azimuth_true = azimuth_targets.cpu().numpy()
                        azimuth_labels.extend(batch_azimuth_true)
                        batch_az_mae = np.mean(np.abs(batch_azimuth_pred - batch_azimuth_true))
                        batch_azimuth_maes.append(batch_az_mae)
                        
                    if elevation_targets is not None:
                        batch_elevation_true = elevation_targets.cpu().numpy()
                        elevation_labels.extend(batch_elevation_true)
                        batch_el_mae = np.mean(np.abs(batch_elevation_pred - batch_elevation_true))
                        batch_elevation_maes.append(batch_el_mae)
                        
                    if azimuth_targets is not None and elevation_targets is not None:
                        batch_combined_mae = (batch_az_mae + batch_el_mae) / 2
                        batch_combined_maes.append(batch_combined_mae)
        
        # 计算整体指标
        metrics = {}
        
        if azimuth_labels:
            azimuth_mae = mean_absolute_error(azimuth_labels, azimuth_predictions)
            azimuth_rmse = np.sqrt(mean_squared_error(azimuth_labels, azimuth_predictions))
            azimuth_std = np.std(batch_azimuth_maes) if len(batch_azimuth_maes) > 1 else 0.0
            metrics['azimuth_mae'] = azimuth_mae
            metrics['azimuth_rmse'] = azimuth_rmse
            metrics['azimuth_std'] = azimuth_std
        
        if elevation_labels:
            elevation_mae = mean_absolute_error(elevation_labels, elevation_predictions)
            elevation_rmse = np.sqrt(mean_squared_error(elevation_labels, elevation_predictions))
            elevation_std = np.std(batch_elevation_maes) if len(batch_elevation_maes) > 1 else 0.0
            metrics['elevation_mae'] = elevation_mae
            metrics['elevation_rmse'] = elevation_rmse
            metrics['elevation_std'] = elevation_std
        
        # 综合指标
        if azimuth_labels and elevation_labels:
            combined_mae = (metrics['azimuth_mae'] + metrics['elevation_mae']) / 2
            combined_std = np.std(batch_combined_maes) if len(batch_combined_maes) > 1 else 0.0
            metrics['combined_mae'] = combined_mae
            metrics['combined_std'] = combined_std
        elif azimuth_labels:
            metrics['combined_mae'] = metrics['azimuth_mae']
            metrics['combined_std'] = metrics.get('azimuth_std', 0.0)
        elif elevation_labels:
            metrics['combined_mae'] = metrics['elevation_mae']
            metrics['combined_std'] = metrics.get('elevation_std', 0.0)
        else:
            metrics['combined_mae'] = float('inf')
            metrics['combined_std'] = 0.0
        
        avg_loss = total_loss / len(val_loader)
        return avg_loss, metrics
    
    def train(self, train_loader, val_loader, num_epochs=100, learning_rate=0.001, 
              patience=10, save_path='best_dual_model.pth'):
        
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=1e-4)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
        
        best_combined_mae = float('inf')
        patience_counter = 0
        
        print(f"开始训练，设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"输出类型: {self.output_type}")
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # 训练
            train_loss = self.train_epoch(train_loader, optimizer, epoch)
            
            # 验证
            val_loss, val_metrics = self.validate_epoch(val_loader)

            # 学习率调度
            scheduler.step(val_metrics.get('combined_mae', val_loss))

            # 记录指标
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            
            for key, value in val_metrics.items():
                if key in self.val_metrics:
                    self.val_metrics[key].append(value)
            
            epoch_time = time.time() - start_time
            
            # 打印信息
            info_str = f'Epoch [{epoch+1:3d}/{num_epochs}] ({epoch_time:.1f}s) | Train: {train_loss:.6f} | Val: {val_loss:.6f}'
            
            if 'azimuth_mae' in val_metrics:
                az_std = val_metrics.get('azimuth_std', 0.0)
                info_str += f" | Az MAE: {val_metrics['azimuth_mae']:.6f} ± {az_std:.6f}"
                
            if 'elevation_mae' in val_metrics:
                el_std = val_metrics.get('elevation_std', 0.0)
                info_str += f" | El MAE: {val_metrics['elevation_mae']:.6f} ± {el_std:.6f}"
                
            if 'combined_mae' in val_metrics:
                combined_std = val_metrics.get('combined_std', 0.0)
                info_str += f" | Combined: {val_metrics['combined_mae']:.6f} ± {combined_std:.6f}"
                
            info_str += f" | LR: {optimizer.param_groups[0]['lr']:.1e}"
            
            print(info_str)
            
            # 早停和模型保存
            current_mae = val_metrics.get('combined_mae', float('inf'))
            if current_mae < best_combined_mae:
                best_combined_mae = current_mae
                patience_counter = 0
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'best_combined_mae': best_combined_mae,
                    'train_losses': self.train_losses,
                    'val_losses': self.val_losses,
                    'val_metrics': self.val_metrics,
                    'output_type': self.output_type,
                    'loss_weights': self.loss_weights
                }, save_path)
                print(f'  ✓ 新的最佳Combined MAE! 模型已保存')
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                print(f'\n早停触发，已连续{patience}个epoch无改善')
                break
        
        return best_combined_mae

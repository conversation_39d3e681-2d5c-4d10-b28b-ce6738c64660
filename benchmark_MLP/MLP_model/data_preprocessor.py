#!/usr/bin/env python3
"""
Quadriga数据预处理模块
包含数据加载、角度计算、归一化等功能
"""

import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import os


class QuadrigaDataset(Dataset):
    """Quadriga数据集类"""
    def __init__(self, channel_data, azimuth_angles=None, elevation_angles=None, 
                 locations=None, path_gains=None):
        self.channel_data = torch.tensor(channel_data, dtype=torch.complex64)
        self.azimuth_angles = torch.tensor(azimuth_angles, dtype=torch.float32) if azimuth_angles is not None else None
        self.elevation_angles = torch.tensor(elevation_angles, dtype=torch.float32) if elevation_angles is not None else None
        self.locations = torch.tensor(locations, dtype=torch.float32) if locations is not None else None
        self.path_gains = torch.tensor(path_gains, dtype=torch.float32) if path_gains is not None else None
        
    def __len__(self):
        return len(self.channel_data)
    
    def __getitem__(self, idx):
        sample = {'channel': self.channel_data[idx]}
        
        if self.azimuth_angles is not None:
            sample['azimuth'] = self.azimuth_angles[idx]
        if self.elevation_angles is not None:
            sample['elevation'] = self.elevation_angles[idx]
        if self.locations is not None:
            sample['location'] = self.locations[idx]
        if self.path_gains is not None:
            sample['path_gain'] = self.path_gains[idx]
            
        return sample


class QuadrigaDataPreprocessor:
    """Quadriga数据预处理器"""
    def __init__(self, data_path, H_ants=8, V_ants=4, nSub=17, no_snapshots=1):
        self.data_path = data_path
        self.H_ants = H_ants
        self.V_ants = V_ants
        self.nSub = nSub
        self.no_snapshots = no_snapshots
        self.UE_port = 4
        self.BS_port = H_ants * V_ants * 2
        
    def load_channel_data(self, filename='H_freq.txt'):
        """加载信道数据"""
        file_path = os.path.join(self.data_path, filename)
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"数据文件不存在: {file_path}")
        
        with open(file_path, 'r') as f:
            lines = f.readlines()
        
        complex_data_list = []
        
        for line in lines:
            if line.strip():
                values = list(map(float, line.strip().split()))
                values = np.array(values)
                
                # 重塑为实部虚部对
                real_imag_pairs = values.reshape(2, -1)
                complex_values = real_imag_pairs[0] + 1j * real_imag_pairs[1]
                
                # 重塑为信道矩阵
                if filename == 'H_freq.txt':
                    channel_matrix = complex_values.reshape(self.BS_port, self.UE_port, self.nSub)
                else:
                    channel_matrix = complex_values.reshape(self.BS_port, self.UE_port, self.no_snapshots)
                
                complex_data_list.append(channel_matrix)
        
        return np.array(complex_data_list)
    
    def load_location_data(self, filename='loc.txt'):
        """加载UE位置数据"""
        file_path = os.path.join(self.data_path, filename)
        
        if not os.path.exists(file_path):
            print(f"警告: 位置文件不存在: {file_path}")
            return None
        
        locations = []
        with open(file_path, 'r') as f:
            for line in f:
                if line.strip():
                    coords = list(map(float, line.strip().split()))
                    locations.append(coords[:3])
        
        return np.array(locations)
    
    def compute_angles_from_locations(self, locations, bs_location=np.array([0, 0, 10])):
        """从UE位置计算方位角和仰角"""
        if locations is None:
            return None, None
            
        # 计算相对位置向量
        rel_positions = locations - bs_location
        
        # 方位角计算
        azimuth_angles = np.arctan2(rel_positions[:, 1], rel_positions[:, 0]) * 180 / np.pi
        
        # 仰角计算
        horizontal_distance = np.sqrt(rel_positions[:, 0]**2 + rel_positions[:, 1]**2)
        elevation_angles = np.arctan2(-rel_positions[:, 2], horizontal_distance) * 180 / np.pi
        
        return azimuth_angles, elevation_angles
    
    def load_path_gain_data(self, filename='pg.txt'):
        """加载路径增益数据"""
        file_path = os.path.join(self.data_path, filename)
        
        if not os.path.exists(file_path):
            print(f"警告: 路径增益文件不存在: {file_path}")
            return None
        
        path_gains = []
        with open(file_path, 'r') as f:
            for line in f:
                if line.strip():
                    pg_value = float(line.strip())
                    path_gains.append(pg_value)
        
        return np.array(path_gains)
    
    def create_covariance_matrix(self, channel_data):
        """创建协方差矩阵用于CNN"""
        N_samples = channel_data.shape[0]
        N_freq_time = channel_data.shape[3] if len(channel_data.shape) == 4 else 1
        
        if len(channel_data.shape) == 3:
            channel_data = channel_data[:, :, :, np.newaxis]
            N_freq_time = 1
            
        cov_matrices = np.zeros((N_samples, N_freq_time, self.BS_port, self.BS_port), dtype=complex)
        
        for sample_idx in range(N_samples):
            for freq_idx in range(N_freq_time):
                H = channel_data[sample_idx, :, :, freq_idx]
                cov_matrices[sample_idx, freq_idx] = np.dot(H, H.conj().T)
        
        return cov_matrices
    
    def normalize_data(self, data, method='standardize'):
        """数据归一化"""
        if data is None:
            return None, None
            
        original_shape = data.shape
        
        if np.iscomplexobj(data):
            real_part = data.real.reshape(-1, 1)
            imag_part = data.imag.reshape(-1, 1)
            
            if method == 'standardize':
                scaler_real = StandardScaler()
                scaler_imag = StandardScaler()
            else:
                scaler_real = MinMaxScaler()
                scaler_imag = MinMaxScaler()
            
            real_normalized = scaler_real.fit_transform(real_part)
            imag_normalized = scaler_imag.fit_transform(imag_part)
            
            normalized_data = (real_normalized + 1j * imag_normalized).reshape(original_shape)
            scaler = (scaler_real, scaler_imag)
        else:
            data_reshaped = data.reshape(-1, 1)
            if method == 'standardize':
                scaler = StandardScaler()
            else:
                scaler = MinMaxScaler()
            
            normalized_data = scaler.fit_transform(data_reshaped).reshape(original_shape)
        
        return normalized_data, scaler

    def normalize_angles_separately(self, azimuth_angles, elevation_angles):
        """分别归一化方位角和仰角到相似的数值范围"""
        # 方位角: -180 to 180 -> -1 to 1
        norm_azimuth = azimuth_angles / 180.0
        
        # 仰角: -90 to 90 -> -1 to 1  
        norm_elevation = elevation_angles / 90.0
        
        return norm_azimuth, norm_elevation


def preprocess_quadriga_data_dual(data_path, model_type='MLP', output_type='dual', 
                                 use_covariance=False, normalize=True, test_split=0.2, 
                                 batch_size=32, bs_location=np.array([0, 0, 10])):
    """双角度数据预处理主函数"""
    print("🔄 开始双角度数据预处理...")
    
    # 初始化预处理器
    preprocessor = QuadrigaDataPreprocessor(data_path)
    
    # 加载数据
    print("📂 加载信道数据...")
    channel_data = preprocessor.load_channel_data()
    
    print("📍 加载位置数据...")
    locations = preprocessor.load_location_data()
    
    print("📊 加载路径增益数据...")
    path_gains = preprocessor.load_path_gain_data()
    
    # 计算角度
    print("📐 计算方位角和仰角...")
    azimuth_angles, elevation_angles = preprocessor.compute_angles_from_locations(locations, bs_location)
    
    # 数据预处理
    if model_type == 'CNN' and use_covariance:
        print("🔄 创建协方差矩阵...")
        channel_data = preprocessor.create_covariance_matrix(channel_data)
    
    # 数据归一化
    channel_scaler = None
    loc_scaler = None
    pg_scaler = None
    
    if normalize:
        print("📏 数据归一化...")
        channel_data, channel_scaler = preprocessor.normalize_data(channel_data)
        if locations is not None:
            locations, loc_scaler = preprocessor.normalize_data(locations)
        if path_gains is not None:
            path_gains, pg_scaler = preprocessor.normalize_data(path_gains)
    
    # 数据格式转换
    if model_type == 'MLP':
        if len(channel_data.shape) == 4:
            channel_data = channel_data.reshape(channel_data.shape[0], -1)
        elif len(channel_data.shape) == 3:
            channel_data = channel_data.reshape(channel_data.shape[0], -1)
    
    # 数据分割
    n_samples = len(channel_data)
    indices = np.random.permutation(n_samples)
    split_idx = int(n_samples * (1 - test_split))
    train_indices = indices[:split_idx]
    val_indices = indices[split_idx:]
    
    # 创建数据集
    train_dataset = QuadrigaDataset(
        channel_data[train_indices],
        azimuth_angles[train_indices] if azimuth_angles is not None else None,
        elevation_angles[train_indices] if elevation_angles is not None else None,
        locations[train_indices] if locations is not None else None,
        path_gains[train_indices] if path_gains is not None else None
    )
    
    val_dataset = QuadrigaDataset(
        channel_data[val_indices],
        azimuth_angles[val_indices] if azimuth_angles is not None else None,
        elevation_angles[val_indices] if elevation_angles is not None else None,
        locations[val_indices] if locations is not None else None,
        path_gains[val_indices] if path_gains is not None else None
    )
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # 数据信息
    input_size = channel_data.shape[1] if len(channel_data.shape) == 2 else np.prod(channel_data.shape[1:])
    
    data_info = {
        'input_size': input_size,
        'n_samples': n_samples,
        'n_train': len(train_indices),
        'n_val': len(val_indices),
        'channel_shape': channel_data.shape,
        'azimuth_range': [azimuth_angles.min(), azimuth_angles.max()] if azimuth_angles is not None else None,
        'elevation_range': [elevation_angles.min(), elevation_angles.max()] if elevation_angles is not None else None,
        'bs_location': bs_location,
        'scalers': {
            'channel': channel_scaler,
            'location': loc_scaler,
            'path_gain': pg_scaler
        }
    }
    
    print("✅ 双角度数据预处理完成!")
    return train_loader, val_loader, data_info

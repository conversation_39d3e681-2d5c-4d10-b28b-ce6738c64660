#!/usr/bin/env python3
"""
模型评估模块
"""

import numpy as np
import torch
import os
from sklearn.metrics import mean_absolute_error, mean_squared_error
from tqdm import tqdm


def evaluate_dual_model(model, test_loader, device, output_type='dual', model_save_path=None):
    """双输出模型评估"""
    if model_save_path and os.path.exists(model_save_path):
        checkpoint = torch.load(model_save_path, map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"✅ 已加载最佳模型 (训练轮数: {checkpoint['epoch']+1})")
    
    model.eval()
    azimuth_predictions = []
    elevation_predictions = []
    azimuth_labels = []
    elevation_labels = []
    
    # 存储每个批次的MAE用于计算标准差
    batch_azimuth_maes = []
    batch_elevation_maes = []
    batch_combined_maes = []
    
    print("🔍 开始最终双角度评估...")
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Evaluating"):
            channel_data = batch['channel'].to(device)
            azimuth_targets = batch.get('azimuth', None)
            elevation_targets = batch.get('elevation', None)

            outputs = model(channel_data)
            
            if output_type == 'dual':
                batch_azimuth_pred = outputs[:, 0].cpu().numpy()
                batch_elevation_pred = outputs[:, 1].cpu().numpy()
                azimuth_predictions.extend(batch_azimuth_pred)
                elevation_predictions.extend(batch_elevation_pred)
                
                if azimuth_targets is not None:
                    batch_azimuth_true = azimuth_targets.cpu().numpy()
                    azimuth_labels.extend(batch_azimuth_true)
                    batch_az_mae = np.mean(np.abs(batch_azimuth_pred - batch_azimuth_true))
                    batch_azimuth_maes.append(batch_az_mae)
                    
                if elevation_targets is not None:
                    batch_elevation_true = elevation_targets.cpu().numpy()
                    elevation_labels.extend(batch_elevation_true)
                    batch_el_mae = np.mean(np.abs(batch_elevation_pred - batch_elevation_true))
                    batch_elevation_maes.append(batch_el_mae)
                    
                if azimuth_targets is not None and elevation_targets is not None:
                    batch_combined_mae = (batch_az_mae + batch_el_mae) / 2
                    batch_combined_maes.append(batch_combined_mae)
    
    # 计算指标
    metrics = {}
    
    if azimuth_labels:
        az_pred = np.array(azimuth_predictions)
        az_true = np.array(azimuth_labels)
        az_residuals = az_pred - az_true
        az_std = np.std(batch_azimuth_maes) if len(batch_azimuth_maes) > 1 else 0.0
        
        metrics['azimuth'] = {
            'MAE': mean_absolute_error(az_true, az_pred),
            'MSE': mean_squared_error(az_true, az_pred),
            'RMSE': np.sqrt(mean_squared_error(az_true, az_pred)),
            'Max_Error': np.max(np.abs(az_residuals)),
            'Std_Error': np.std(az_residuals),
            'MAE_STD': az_std,
            'Predictions': az_pred,
            'Labels': az_true,
            'Residuals': az_residuals
        }
    
    if elevation_labels:
        el_pred = np.array(elevation_predictions)
        el_true = np.array(elevation_labels)
        el_residuals = el_pred - el_true
        el_std = np.std(batch_elevation_maes) if len(batch_elevation_maes) > 1 else 0.0
        
        metrics['elevation'] = {
            'MAE': mean_absolute_error(el_true, el_pred),
            'MSE': mean_squared_error(el_true, el_pred),
            'RMSE': np.sqrt(mean_squared_error(el_true, el_pred)),
            'Max_Error': np.max(np.abs(el_residuals)),
            'Std_Error': np.std(el_residuals),
            'MAE_STD': el_std,
            'Predictions': el_pred,
            'Labels': el_true,
            'Residuals': el_residuals
        }
    
    # 综合指标
    if azimuth_labels and elevation_labels:
        combined_mae = (metrics['azimuth']['MAE'] + metrics['elevation']['MAE']) / 2
        combined_std = np.std(batch_combined_maes) if len(batch_combined_maes) > 1 else 0.0
        metrics['combined_mae'] = combined_mae
        metrics['combined_std'] = combined_std
    
    return metrics


def print_dual_results(metrics, output_type='dual'):
    """打印双角度最终结果"""
    print("\n" + "="*80)
    print("🎯 双角度估计最终评估结果")
    print("="*80)
    
    if 'azimuth' in metrics:
        print(f"📊 方位角 (Azimuth) 指标:")
        az_metrics = metrics['azimuth']
        mae_std = az_metrics.get('MAE_STD', 0.0)
        print(f"   MAE:                 {az_metrics['MAE']:.6f}° ± {mae_std:.6f}°")
        print(f"   RMSE:                {az_metrics['RMSE']:.6f}°")
        print(f"   最大误差:            {az_metrics['Max_Error']:.6f}°")
        print(f"   误差标准差:          {az_metrics['Std_Error']:.6f}°")
    
    if 'elevation' in metrics:
        print(f"\n📊 仰角 (Elevation) 指标:")
        el_metrics = metrics['elevation']
        mae_std = el_metrics.get('MAE_STD', 0.0)
        print(f"   MAE:                 {el_metrics['MAE']:.6f}° ± {mae_std:.6f}°")
        print(f"   RMSE:                {el_metrics['RMSE']:.6f}°")
        print(f"   最大误差:            {el_metrics['Max_Error']:.6f}°")
        print(f"   误差标准差:          {el_metrics['Std_Error']:.6f}°")
    
    if 'combined_mae' in metrics:
        print(f"\n📊 综合指标:")
        combined_std = metrics.get('combined_std', 0.0)
        print(f"   综合MAE:             {metrics['combined_mae']:.6f}° ± {combined_std:.6f}°")
    
    print("="*80)
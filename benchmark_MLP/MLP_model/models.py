#!/usr/bin/env python3
"""
复数神经网络模型定义
包含MLP和CNN的双输出模型
"""

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F

# 尝试导入complexPyTorch库
try:
    from complexPyTorch.complexLayers import ComplexBatchNorm2d, ComplexConv2d, ComplexLinear
    from complexPyTorch.complexFunctions import complex_relu, complex_max_pool2d
except ImportError:
    print("警告: complexPyTorch库未安装，将使用模拟的复数层")
    
    # 模拟复数层定义
    class ComplexLinear(nn.Module):
        def __init__(self, in_features, out_features):
            super().__init__()
            self.real_linear = nn.Linear(in_features, out_features)
            self.imag_linear = nn.Linear(in_features, out_features)
            
        def forward(self, x):
            real_out = self.real_linear(x.real) - self.imag_linear(x.imag)
            imag_out = self.real_linear(x.imag) + self.imag_linear(x.real)
            return torch.complex(real_out, imag_out)
    
    class ComplexConv2d(nn.Module):
        def __init__(self, in_channels, out_channels, kernel_size, stride=1, padding=0):
            super().__init__()
            self.real_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
            self.imag_conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride, padding)
            
        def forward(self, x):
            real_out = self.real_conv(x.real) - self.imag_conv(x.imag)
            imag_out = self.real_conv(x.imag) + self.imag_conv(x.real)
            return torch.complex(real_out, imag_out)
    
    class ComplexBatchNorm2d(nn.Module):
        def __init__(self, num_features):
            super().__init__()
            self.real_bn = nn.BatchNorm2d(num_features)
            self.imag_bn = nn.BatchNorm2d(num_features)
            
        def forward(self, x):
            return torch.complex(self.real_bn(x.real), self.imag_bn(x.imag))
    
    def complex_relu(x):
        return torch.complex(torch.relu(x.real), torch.relu(x.imag))


class Flatten(nn.Module):
    def forward(self, input):
        return input.view(input.size(0), -1)


class HH2ComplexMLPDual(nn.Module):
    """双输出复数MLP模型，同时估计方位角和仰角"""
    def __init__(self, input_size, device, output_type='dual'):
        super().__init__()
        self.output_type = output_type
        
        # 共享特征提取层
        self.shared_layers = nn.ModuleList([
            ComplexLinear(input_size, input_size // 2),
            ComplexLinear(input_size // 2, input_size // 4),
            ComplexLinear(input_size // 4, input_size // 8),
        ])
        
        # 专用输出头
        if output_type == 'dual' or output_type == 'azimuth':
            self.azimuth_head = ComplexLinear(input_size // 8, 1)
        
        if output_type == 'dual' or output_type == 'elevation':
            self.elevation_head = ComplexLinear(input_size // 8, 1)
            
        # 注意力机制用于特征融合
        if output_type == 'dual':
            self.attention = ComplexLinear(input_size // 8, 2)
        
    def forward(self, x):
        # 共享特征提取
        for layer in self.shared_layers:
            x = complex_relu(layer(x))
        
        if self.output_type == 'dual':
            # 改进的注意力机制 - 使用温度参数控制注意力分布
            temperature = 2.0  # 较高温度使注意力分布更均匀
            attention_logits = self.attention(x).abs() / temperature
            attention_weights = torch.softmax(attention_logits, dim=1)
            
            # 确保注意力权重不会过于极端
            attention_weights = torch.clamp(attention_weights, min=0.1, max=0.9)
            
            # 加权特征
            az_features = x * attention_weights[:, 0:1]
            el_features = x * attention_weights[:, 1:2]
            
            # 分别输出
            azimuth_complex = self.azimuth_head(az_features)
            elevation_complex = self.elevation_head(el_features)
            
            # 角度转换
            azimuth_angle = (180/np.pi) * azimuth_complex.angle()
            elevation_angle = (90/np.pi) * elevation_complex.angle()
            
            return torch.cat([azimuth_angle, elevation_angle], dim=1)
            
        elif self.output_type == 'azimuth':
            azimuth_complex = self.azimuth_head(x)
            azimuth_angle = (180/np.pi) * azimuth_complex.angle()
            return azimuth_angle
            
        elif self.output_type == 'elevation':
            elevation_complex = self.elevation_head(x)
            elevation_angle = (90/np.pi) * elevation_complex.angle()
            return elevation_angle


class HH2ComplexCONVDual(nn.Module):
    """双输出复数CNN模型，同时估计方位角和仰角"""
    def __init__(self, input_size, device, output_type='dual'):
        super().__init__()
        self.output_type = output_type
        self.flatten = Flatten()
        
        # 卷积层
        self.conv_layers = nn.ModuleList([
            ComplexConv2d(1, 16, kernel_size=(3,3), stride=1, padding=1),
            ComplexBatchNorm2d(16),
            ComplexConv2d(16, 32, kernel_size=(3,3), stride=1, padding=1),
            ComplexBatchNorm2d(32),
            ComplexConv2d(32, 64, kernel_size=(3,3), stride=1, padding=1),
            ComplexBatchNorm2d(64),
        ])
        
        # 全连接层
        self.shared_fc = nn.ModuleList([
            ComplexLinear(input_size, input_size // 2),
            ComplexLinear(input_size // 2, input_size // 4),
        ])
        
        # 专用输出头
        if output_type == 'dual' or output_type == 'azimuth':
            self.azimuth_head = ComplexLinear(input_size // 4, 1)
        
        if output_type == 'dual' or output_type == 'elevation':
            self.elevation_head = ComplexLinear(input_size // 4, 1)
            
        # 跨注意力机制
        if output_type == 'dual':
            self.cross_attention = ComplexLinear(input_size // 4, input_size // 4)
        
    def forward(self, x):
        # 卷积特征提取
        for i in range(0, len(self.conv_layers), 2):
            conv_layer = self.conv_layers[i]
            bn_layer = self.conv_layers[i+1]
            x = complex_relu(bn_layer(conv_layer(x)))
        
        # 展平并通过全连接层
        x = x.view(x.size(0), -1)
        for fc_layer in self.shared_fc:
            x = complex_relu(fc_layer(x))
        
        if self.output_type == 'dual':
            # 跨注意力机制
            attended_features = complex_relu(self.cross_attention(x))
            
            # 分别输出
            azimuth_complex = self.azimuth_head(x + attended_features * 0.1)
            elevation_complex = self.elevation_head(x + attended_features * 0.1)
            
            # 角度转换
            azimuth_angle = (180/np.pi) * azimuth_complex.angle()
            elevation_angle = (90/np.pi) * elevation_complex.angle()
            
            return torch.cat([azimuth_angle, elevation_angle], dim=1)
            
        elif self.output_type == 'azimuth':
            azimuth_complex = self.azimuth_head(x)
            return (180/np.pi) * azimuth_complex.angle()
            
        elif self.output_type == 'elevation':
            elevation_complex = self.elevation_head(x)
            return (90/np.pi) * elevation_complex.angle()


class HH2ComplexMLP(nn.Module):
    """单输出复数MLP模型"""
    def __init__(self, input_size, device, n_outputs=1):
        super().__init__()
        self.flatten = Flatten()
        self.mlp_1 = ComplexLinear(input_size, input_size // 2)
        self.mlp_2 = ComplexLinear(input_size // 2, input_size // 4)
        self.mlp_3 = ComplexLinear(input_size // 4, input_size // 8)
        self.mlp_5 = ComplexLinear(input_size // 8, n_outputs)
        
    def forward(self, x):
        x = self.mlp_1(x)
        x = complex_relu(x)
        x = self.mlp_2(x)
        x = complex_relu(x)
        x = self.mlp_3(x)
        x = complex_relu(x)
        x = self.mlp_5(x)
        # 将复数角度从-π到π映射到-180到180度
        output = (180/np.pi) * x.angle()
        return output

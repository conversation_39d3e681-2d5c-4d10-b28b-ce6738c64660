#!/usr/bin/env python3
"""
结果可视化模块
"""

import matplotlib.pyplot as plt
import numpy as np
import os


def plot_dual_results(trainer, metrics, output_type='dual', save_dir='./results/'):
    """绘制双角度训练和评估结果"""
    os.makedirs(save_dir, exist_ok=True)

    if output_type == 'dual':
        # 双角度结果图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        epochs = range(1, len(trainer.train_losses) + 1)
        
        # 训练损失
        axes[0,0].plot(epochs, trainer.train_losses, 'b-', label='Train Loss', linewidth=2)
        axes[0,0].plot(epochs, trainer.val_losses, 'r-', label='Val Loss', linewidth=2)
        axes[0,0].set_xlabel('Epoch')
        axes[0,0].set_ylabel('Loss')
        axes[0,0].set_title('Training and Validation Loss')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        
        # MAE曲线（带误差棒）
        if 'azimuth_mae' in trainer.val_metrics and trainer.val_metrics['azimuth_mae']:
            az_mae = trainer.val_metrics['azimuth_mae']
            az_std = trainer.val_metrics.get('azimuth_std', [0] * len(az_mae))
            axes[0,1].errorbar(epochs, az_mae, yerr=az_std, label='Azimuth MAE', 
                              capsize=3, capthick=1, linewidth=2)
        
        if 'elevation_mae' in trainer.val_metrics and trainer.val_metrics['elevation_mae']:
            el_mae = trainer.val_metrics['elevation_mae']
            el_std = trainer.val_metrics.get('elevation_std', [0] * len(el_mae))
            axes[0,1].errorbar(epochs, el_mae, yerr=el_std, label='Elevation MAE',
                              capsize=3, capthick=1, linewidth=2)
        
        if 'combined_mae' in trainer.val_metrics and trainer.val_metrics['combined_mae']:
            combined_mae = trainer.val_metrics['combined_mae']
            combined_std = trainer.val_metrics.get('combined_std', [0] * len(combined_mae))
            axes[0,1].errorbar(epochs, combined_mae, yerr=combined_std, label='Combined MAE',
                              capsize=3, capthick=1, linewidth=2)
        
        axes[0,1].set_xlabel('Epoch')
        axes[0,1].set_ylabel('MAE (degrees)')
        axes[0,1].set_title('MAE with Error Bars')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)
        
        # 方位角散点图
        if 'azimuth' in metrics:
            az_pred = metrics['azimuth']['Predictions']
            az_true = metrics['azimuth']['Labels']
            axes[0,2].scatter(az_true, az_pred, alpha=0.6, s=20)
            axes[0,2].plot([az_true.min(), az_true.max()], [az_true.min(), az_true.max()], 'r--', lw=2)
            axes[0,2].set_xlabel('True Azimuth (degrees)')
            axes[0,2].set_ylabel('Predicted Azimuth (degrees)')
            axes[0,2].set_title(f'Azimuth: MAE={metrics["azimuth"]["MAE"]:.3f}°')
            axes[0,2].grid(True, alpha=0.3)
        
        # 仰角散点图
        if 'elevation' in metrics:
            el_pred = metrics['elevation']['Predictions']
            el_true = metrics['elevation']['Labels']
            axes[1,0].scatter(el_true, el_pred, alpha=0.6, s=20, color='green')
            axes[1,0].plot([el_true.min(), el_true.max()], [el_true.min(), el_true.max()], 'r--', lw=2)
            axes[1,0].set_xlabel('True Elevation (degrees)')
            axes[1,0].set_ylabel('Predicted Elevation (degrees)')
            axes[1,0].set_title(f'Elevation: MAE={metrics["elevation"]["MAE"]:.3f}°')
            axes[1,0].grid(True, alpha=0.3)
        
        # 残差分布
        if 'azimuth' in metrics and 'elevation' in metrics:
            az_residuals = metrics['azimuth']['Residuals']
            el_residuals = metrics['elevation']['Residuals']
            
            axes[1,1].hist(az_residuals, bins=30, alpha=0.7, label=f'Az (σ={np.std(az_residuals):.3f}°)',
                         color='blue', density=True)
            axes[1,1].hist(el_residuals, bins=30, alpha=0.7, label=f'El (σ={np.std(el_residuals):.3f}°)',
                         color='green', density=True)
            axes[1,1].set_xlabel('Residuals (degrees)')
            axes[1,1].set_ylabel('Density')
            axes[1,1].set_title('Residual Distribution')
            axes[1,1].legend()
            axes[1,1].grid(True, alpha=0.3)
        
        # 综合误差分布
        if 'azimuth' in metrics and 'elevation' in metrics:
            az_errors = np.abs(metrics['azimuth']['Residuals'])
            el_errors = np.abs(metrics['elevation']['Residuals'])
            
            axes[1,2].hist(az_errors, bins=30, alpha=0.7, label=f'Az (MAE={metrics["azimuth"]["MAE"]:.3f}°)',
                         color='blue', density=True)
            axes[1,2].hist(el_errors, bins=30, alpha=0.7, label=f'El (MAE={metrics["elevation"]["MAE"]:.3f}°)',
                         color='green', density=True)
            axes[1,2].set_xlabel('Absolute Error (degrees)')
            axes[1,2].set_ylabel('Density')
            axes[1,2].set_title('Error Distribution Comparison')
            axes[1,2].legend()
            axes[1,2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'dual_angle_results.png'), dpi=300, bbox_inches='tight')
        plt.show()
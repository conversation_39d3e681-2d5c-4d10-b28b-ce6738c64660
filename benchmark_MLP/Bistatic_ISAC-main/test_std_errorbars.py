#!/usr/bin/env python3
"""
测试标准差误差棒功能的简单脚本
"""

import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from sklearn.metrics import mean_absolute_error, mean_squared_error
import os
import sys

# 添加当前目录到路径
sys.path.append('.')

# 直接运行修改后的train_MLP.PY来测试功能
import subprocess
import sys

def test_std_errorbars():
    """通过运行train_MLP.PY来测试标准差误差棒功能"""
    print("🧪 开始测试标准差误差棒功能...")
    print("📊 运行修改后的train_MLP.PY...")

    try:
        # 运行train_MLP.PY
        result = subprocess.run([sys.executable, "train_MLP.PY"],
                              capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print("✅ train_MLP.PY 运行成功!")
            print("📊 输出:")
            print(result.stdout)

            # 检查是否生成了预期的文件
            expected_files = [
                './results/training_summary.png',
                './results/training_summary_with_std.png',
                './results/detailed_error_analysis_with_std.png',
                './results/detailed_evaluation.png',
                './results/training_results.npz'
            ]

            existing_files = []
            for file_path in expected_files:
                if os.path.exists(file_path):
                    existing_files.append(file_path)

            print(f"\n📁 生成的文件 ({len(existing_files)}/{len(expected_files)}):")
            for file_path in existing_files:
                print(f"   ✓ {file_path}")

            missing_files = [f for f in expected_files if f not in existing_files]
            if missing_files:
                print(f"\n❌ 缺失的文件:")
                for file_path in missing_files:
                    print(f"   ✗ {file_path}")

            return len(existing_files) >= 3  # 至少生成3个文件就算成功

        else:
            print("❌ train_MLP.PY 运行失败!")
            print("错误输出:")
            print(result.stderr)
            return False

    except subprocess.TimeoutExpired:
        print("⏰ 运行超时!")
        return False
    except Exception as e:
        print(f"💥 运行过程中出现错误: {e}")
        return False



if __name__ == "__main__":
    print("="*60)
    print("🧪 标准差误差棒功能测试")
    print("="*60)
    
    try:
        success = test_std_errorbars()
        if success:
            print("\n🎉 所有测试通过!")
        else:
            print("\n❌ 测试失败!")
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

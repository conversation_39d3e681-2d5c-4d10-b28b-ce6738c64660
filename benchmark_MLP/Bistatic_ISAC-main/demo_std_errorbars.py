#!/usr/bin/env python3
"""
标准差误差棒功能演示脚本
展示如何使用新增的统计分析功能
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt

def demo_std_errorbars():
    """演示标准差误差棒功能"""
    
    print("🎯 标准差误差棒功能演示")
    print("="*60)
    
    # 检查是否存在结果文件
    results_dir = './results/'
    expected_files = [
        'training_summary_with_std.png',
        'detailed_error_analysis_with_std.png',
        'training_results.npz'
    ]
    
    print("📁 检查生成的文件...")
    existing_files = []
    for file_name in expected_files:
        file_path = os.path.join(results_dir, file_name)
        if os.path.exists(file_path):
            existing_files.append(file_name)
            print(f"   ✓ {file_name}")
        else:
            print(f"   ✗ {file_name} (未找到)")
    
    if not existing_files:
        print("\n❌ 未找到结果文件，请先运行 train_MLP.PY")
        print("   运行命令: python train_MLP.PY")
        return False
    
    # 加载并分析结果数据
    results_file = os.path.join(results_dir, 'training_results.npz')
    if os.path.exists(results_file):
        print(f"\n📊 加载结果数据: {results_file}")
        
        try:
            data = np.load(results_file, allow_pickle=True)
            
            print("📋 数据内容:")
            for key in data.files:
                if key == 'detailed_stats':
                    print(f"   ✓ {key}: 详细统计信息")
                    stats = data[key].item()
                    if isinstance(stats, dict):
                        for stat_key, stat_value in stats.items():
                            print(f"      - {stat_key}: {stat_value}")
                else:
                    array_data = data[key]
                    if hasattr(array_data, 'shape'):
                        print(f"   ✓ {key}: shape {array_data.shape}")
                    else:
                        print(f"   ✓ {key}: {type(array_data)}")
            
            # 分析预测结果
            if 'predictions' in data and 'labels' in data:
                predictions = data['predictions']
                labels = data['labels']
                residuals = data['residuals']
                
                print(f"\n📈 预测结果分析:")
                print(f"   样本数量: {len(predictions)}")
                print(f"   预测值范围: [{predictions.min():.3f}, {predictions.max():.3f}]")
                print(f"   真实值范围: [{labels.min():.3f}, {labels.max():.3f}]")
                print(f"   残差范围: [{residuals.min():.3f}, {residuals.max():.3f}]")
                
                # 计算额外统计信息
                mae = np.mean(np.abs(residuals))
                std_error = np.std(residuals)
                cv = std_error / mae if mae > 0 else 0
                
                print(f"   MAE: {mae:.6f}°")
                print(f"   标准差: {std_error:.6f}°")
                print(f"   变异系数: {cv:.3f}")
                
                # 置信区间
                confidence_levels = [50, 68, 95, 99]
                abs_errors = np.abs(residuals)
                print(f"\n📊 误差置信区间:")
                for conf in confidence_levels:
                    percentile_val = np.percentile(abs_errors, conf)
                    print(f"   {conf:2d}%: ≤ {percentile_val:.4f}°")
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    # 显示图片信息
    print(f"\n🖼️  生成的图表:")
    image_files = [f for f in existing_files if f.endswith('.png')]
    for img_file in image_files:
        img_path = os.path.join(results_dir, img_file)
        file_size = os.path.getsize(img_path) / 1024  # KB
        print(f"   📊 {img_file} ({file_size:.1f} KB)")
    
    # 功能特性说明
    print(f"\n✨ 新增功能特性:")
    features = [
        "训练曲线带标准差误差棒",
        "MAE曲线带标准差误差棒", 
        "预测结果按组显示标准差",
        "误差分布正态性拟合",
        "残差时间序列分析",
        "Q-Q图正态性检验",
        "累积误差分布图",
        "误差vs预测值趋势分析",
        "多级置信区间显示",
        "详细统计信息输出"
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"   {i:2d}. {feature}")
    
    print(f"\n📖 使用说明:")
    print(f"   1. 运行 train_MLP.PY 进行训练")
    print(f"   2. 查看 ./results/ 目录下的图表文件")
    print(f"   3. 重点关注带 '_with_std' 后缀的图表")
    print(f"   4. 查看控制台输出的详细统计信息")
    
    print(f"\n🎉 演示完成!")
    return True

def show_usage_examples():
    """显示使用示例"""
    print(f"\n💡 使用示例:")
    print(f"="*60)
    
    examples = [
        {
            "title": "基础训练",
            "command": "python train_MLP.PY",
            "description": "运行完整训练流程，生成所有图表"
        },
        {
            "title": "快速测试",
            "command": "python test_std_errorbars.py", 
            "description": "快速测试标准差误差棒功能"
        },
        {
            "title": "查看结果",
            "command": "python demo_std_errorbars.py",
            "description": "分析和展示训练结果"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['title']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")
        print()

if __name__ == "__main__":
    print("🚀 标准差误差棒功能演示程序")
    print("="*60)
    
    try:
        success = demo_std_errorbars()
        
        if success:
            show_usage_examples()
        else:
            print("\n💡 建议:")
            print("   1. 确保已运行 train_MLP.PY")
            print("   2. 检查 ./results/ 目录是否存在")
            print("   3. 确认Python环境配置正确")
            
    except Exception as e:
        print(f"\n💥 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

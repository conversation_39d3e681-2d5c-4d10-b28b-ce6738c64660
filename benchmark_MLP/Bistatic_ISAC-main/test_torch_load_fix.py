#!/usr/bin/env python3
"""
测试torch.load修复的脚本
验证weights_only=False参数是否正确解决了PyTorch 2.6的兼容性问题
"""

import torch
import numpy as np
import os
import tempfile

def test_torch_load_fix():
    """测试torch.load修复"""
    print("🧪 测试torch.load修复...")
    
    # 创建一个包含numpy对象的测试检查点
    test_data = {
        'model_state_dict': {'weight': torch.randn(10, 5)},
        'optimizer_state_dict': {'param_groups': [{'lr': 0.001}]},
        'epoch': 5,
        'best_mae': 7.5,
        'train_losses': [10.0, 8.5, 7.2, 6.8, 6.5],
        'val_losses': [12.0, 10.5, 9.2, 8.8, 8.5],
        'val_maes': [8.0, 7.8, 7.6, 7.5, 7.5],
        'val_rmses': [10.0, 9.8, 9.6, 9.5, 9.5],
        'val_stds': [1.2, 1.1, 1.0, 0.9, 0.8],
        'numpy_scalar': np.float64(3.14159),  # 这会导致原来的错误
        'numpy_array': np.array([1, 2, 3, 4, 5])
    }
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.pth', delete=False) as tmp_file:
        temp_path = tmp_file.name
    
    try:
        # 保存检查点
        print(f"💾 保存测试检查点到: {temp_path}")
        torch.save(test_data, temp_path)
        
        # 测试旧方式加载（应该失败）
        print("❌ 测试旧方式加载 (weights_only=True, 默认)...")
        try:
            checkpoint_old = torch.load(temp_path)  # 默认weights_only=True在PyTorch 2.6+
            print("⚠️  意外成功 - 可能是旧版本PyTorch")
        except Exception as e:
            print(f"✅ 预期的错误: {type(e).__name__}")
            print(f"   错误信息: {str(e)[:100]}...")
        
        # 测试新方式加载（应该成功）
        print("✅ 测试新方式加载 (weights_only=False)...")
        try:
            checkpoint_new = torch.load(temp_path, weights_only=False)
            print("✅ 成功加载检查点!")
            
            # 验证数据完整性
            assert checkpoint_new['epoch'] == 5
            assert len(checkpoint_new['train_losses']) == 5
            assert len(checkpoint_new['val_stds']) == 5
            assert np.isclose(checkpoint_new['numpy_scalar'], 3.14159)
            assert np.array_equal(checkpoint_new['numpy_array'], np.array([1, 2, 3, 4, 5]))
            
            print("✅ 数据完整性验证通过!")
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
            
    finally:
        # 清理临时文件
        if os.path.exists(temp_path):
            os.unlink(temp_path)
            print(f"🗑️  清理临时文件: {temp_path}")
    
    print("🎉 torch.load修复测试完成!")
    return True

def test_evaluate_model_function():
    """测试evaluate_model函数中的torch.load调用"""
    print("\n🧪 测试evaluate_model函数...")
    
    # 检查train_MLP.PY文件中的torch.load调用
    script_path = "train_MLP.PY"
    if not os.path.exists(script_path):
        print(f"❌ 找不到文件: {script_path}")
        return False
    
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否包含正确的torch.load调用
    if 'torch.load(model_save_path, map_location=device, weights_only=False)' in content:
        print("✅ 找到正确的torch.load调用 (weights_only=False)")
        return True
    elif 'torch.load(model_save_path, map_location=device)' in content:
        print("❌ 找到旧的torch.load调用 (缺少weights_only=False)")
        return False
    else:
        print("❌ 未找到torch.load调用")
        return False

if __name__ == "__main__":
    print("🚀 PyTorch 2.6兼容性测试开始")
    print("=" * 50)
    
    # 显示PyTorch版本
    print(f"📦 PyTorch版本: {torch.__version__}")
    
    # 运行测试
    test1_result = test_torch_load_fix()
    test2_result = test_evaluate_model_function()
    
    print("\n" + "=" * 50)
    if test1_result and test2_result:
        print("🎉 所有测试通过! torch.load修复成功!")
    else:
        print("❌ 部分测试失败，请检查修复。")
    
    print("=" * 50)

# STD标准差误差棒修改总结

## 修改概述

成功为 `train_MLP.py` 添加了标准差（STD）误差棒的输出，并将std结果修改为MAE的std。

## 主要修改内容

### 1. QuadrigaTrainer类修改

#### 1.1 初始化方法 (`__init__`)
- **添加**: `self.val_stds = []` - 用于存储MAE标准差的列表

#### 1.2 验证方法 (`validate_epoch`)
- **添加**: `batch_maes = []` - 存储每个批次的MAE
- **修改**: 在每个批次中计算MAE并存储到 `batch_maes`
- **添加**: 计算MAE的标准差：`mae_std = np.std(batch_maes) if len(batch_maes) > 1 else 0.0`
- **修改**: 返回值增加 `mae_std`：`return avg_loss, mae, rmse, mae_std, all_predictions, all_labels`

#### 1.3 训练方法 (`train`)
- **修改**: 更新验证调用以接收新的返回值：`val_loss, val_mae, val_rmse, mae_std, predictions, labels = self.validate_epoch(...)`
- **添加**: 记录MAE标准差：`self.val_stds.append(mae_std)`
- **修改**: 训练输出显示MAE±STD：`MAE: {val_mae:.6f} ± {mae_std:.6f}`
- **修改**: 模型保存时包含STD数据：`'val_stds': self.val_stds`

### 2. 评估函数修改 (`evaluate_model`)

- **添加**: `batch_maes = []` - 存储每个批次的MAE
- **修改**: 在每个批次中计算MAE并存储
- **添加**: 计算MAE的标准差：`mae_std = np.std(batch_maes) if len(batch_maes) > 1 else 0.0`
- **修改**: metrics字典增加：`'MAE_STD': mae_std`

### 3. 结果打印函数修改 (`print_final_results`)

- **修改**: MAE显示格式：`MAE (平均绝对误差): {metrics['MAE']:.6f} ± {metrics['MAE_STD']:.6f}`
- **修改**: 最终结果显示：`最终MAE结果: {metrics['MAE']:.6f} ± {metrics['MAE_STD']:.6f}`

### 4. 绘图函数修改

#### 4.1 `plot_results` 函数
- **修改**: MAE曲线添加误差棒支持
- **添加**: 检查是否有STD数据：`if hasattr(trainer, 'val_stds') and len(trainer.val_stds) > 0:`
- **修改**: 使用 `errorbar` 绘制MAE曲线：`axes[1].errorbar(epochs, trainer.val_maes, yerr=trainer.val_stds, ...)`
- **修改**: 标题更新为：`'Validation MAE with Standard Deviation'`

#### 4.2 `plot_results_with_std_errorbars` 函数
- **修改**: 优先使用实际的MAE标准差而不是滑动窗口计算的标准差
- **添加**: 条件判断使用实际STD或滑动窗口STD
- **修改**: 标题根据使用的STD类型动态调整

## 技术实现细节

### MAE标准差计算方法
1. **批次级别计算**: 在每个验证批次中计算MAE
2. **标准差计算**: 使用 `np.std(batch_maes)` 计算所有批次MAE的标准差
3. **边界处理**: 当批次数量≤1时，STD设为0.0

### 误差棒显示
- **训练过程**: 每个epoch显示 `MAE: value ± std`
- **最终结果**: 显示 `MAE: value ± std`
- **图表**: 使用matplotlib的errorbar功能显示误差棒

## 验证结果

从测试输出可以看到修改成功：
```
Epoch [  1/100] (4.5s) | Train: 134.322955 | Val: 85.307484 | MAE: 7.986136 ± 1.115508 | RMSE: 9.521693 | LR: 1.0e-04
Epoch [  2/100] (4.6s) | Train: 61.382382 | Val: 102.362946 | MAE: 8.491158 ± 1.339559 | RMSE: 10.427172 | LR: 1.0e-04
```

## 兼容性

- **向后兼容**: 修改保持了原有功能的完整性
- **可选功能**: 如果没有STD数据，系统会优雅降级到原有显示方式
- **数据完整性**: 所有原有的训练和评估功能保持不变

## 文件修改列表

1. `train_MLP.PY` - 主要修改文件
   - QuadrigaTrainer类
   - evaluate_model函数
   - print_final_results函数
   - plot_results函数
   - plot_results_with_std_errorbars函数

## 使用说明

修改后的代码会自动计算和显示MAE的标准差，无需额外配置。标准差信息会在以下位置显示：

1. **训练过程中**: 每个epoch的输出
2. **最终评估**: 评估结果摘要
3. **图表**: 训练曲线的误差棒
4. **保存的模型**: 包含在检查点文件中

这些修改提供了更全面的模型性能评估，帮助用户更好地理解模型预测的不确定性和稳定性。

## PyTorch 2.6兼容性修复

### 问题描述
在PyTorch 2.6+版本中，`torch.load`的默认行为从`weights_only=False`改为`weights_only=True`，这导致加载包含numpy对象的检查点文件时出现错误：

```
UnpicklingError: Weights only load failed. This file can still be loaded, to do so you have two options...
WeightsUnpickler error: Unsupported global: GLOBAL numpy._core.multiarray.scalar was not an allowed global by default.
```

### 修复方案
在`evaluate_model`函数中的`torch.load`调用添加`weights_only=False`参数：

```python
# 修复前
checkpoint = torch.load(model_save_path, map_location=device)

# 修复后
checkpoint = torch.load(model_save_path, map_location=device, weights_only=False)
```

### 验证结果
- ✅ 成功解决PyTorch 2.6+版本的兼容性问题
- ✅ 保持向后兼容性（旧版本PyTorch仍然正常工作）
- ✅ 通过专门的测试脚本验证修复有效性
- ✅ 训练过程正常运行，无错误发生

### 安全说明
使用`weights_only=False`会允许加载任意Python对象，这在加载不受信任的文件时可能存在安全风险。但在我们的场景中，检查点文件是由同一个训练脚本生成的，因此是安全的。

#!/usr/bin/env python3
"""
测试STD修改的简单脚本
"""
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入修改后的模块
import importlib.util
import os

# 获取完整路径
current_dir = os.path.dirname(os.path.abspath(__file__))
train_mlp_path = os.path.join(current_dir, "train_MLP.PY")

spec = importlib.util.spec_from_file_location("train_MLP", train_mlp_path)
train_MLP = importlib.util.module_from_spec(spec)
spec.loader.exec_module(train_MLP)

QuadrigaTrainer = train_MLP.QuadrigaTrainer
QuadrigaDataset = train_MLP.QuadrigaDataset
HH2ComplexMLP = train_MLP.HH2ComplexMLP
evaluate_model = train_MLP.evaluate_model
print_final_results = train_MLP.print_final_results

# 创建简单的测试数据
class SimpleTestDataset(Dataset):
    def __init__(self, n_samples=100, input_size=1000):
        # 生成随机复数信道数据
        self.channel_data = torch.randn(n_samples, input_size, dtype=torch.complex64)
        # 生成随机角度标签 (-180 到 180 度)
        self.labels = torch.randn(n_samples) * 180
        
    def __len__(self):
        return len(self.channel_data)
    
    def __getitem__(self, idx):
        return {
            'channel': self.channel_data[idx],
            'label': self.labels[idx]
        }

def test_std_modifications():
    """测试STD修改"""
    print("🧪 测试STD修改...")
    
    # 设备选择
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 创建测试数据
    input_size = 1000
    train_dataset = SimpleTestDataset(n_samples=200, input_size=input_size)
    val_dataset = SimpleTestDataset(n_samples=50, input_size=input_size)
    
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    
    # 创建简单模型
    model = HH2ComplexMLP(input_size=input_size, device=device, 
                         hidden_dims=[256, 128, 64], dropout_rate=0.1)
    
    # 创建训练器
    trainer = QuadrigaTrainer(model, device)
    
    # 短期训练测试
    print("\n🏃‍♂️ 开始短期训练测试...")
    best_mae = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=5,  # 只训练5个epoch用于测试
        learning_rate=0.01,
        patience=10,
        save_path='test_model.pth'
    )
    
    print(f"\n✅ 训练完成! 最佳MAE: {best_mae:.6f}")
    
    # 检查是否正确记录了STD
    if hasattr(trainer, 'val_stds') and len(trainer.val_stds) > 0:
        print(f"✅ STD记录成功! 最后一个epoch的STD: {trainer.val_stds[-1]:.6f}")
        print(f"📊 所有epoch的STD: {[f'{std:.6f}' for std in trainer.val_stds]}")
    else:
        print("❌ STD记录失败!")
        return False
    
    # 测试评估函数
    print("\n🔍 测试评估函数...")
    metrics = evaluate_model(model, val_loader, device, 'test_model.pth')
    
    # 检查是否包含MAE_STD
    if 'MAE_STD' in metrics:
        print(f"✅ 评估函数STD计算成功! MAE_STD: {metrics['MAE_STD']:.6f}")
    else:
        print("❌ 评估函数STD计算失败!")
        return False
    
    # 测试打印函数
    print("\n📋 测试打印函数...")
    print_final_results(metrics)
    
    # 清理测试文件
    if os.path.exists('test_model.pth'):
        os.remove('test_model.pth')
    
    print("\n🎉 所有测试通过!")
    return True

if __name__ == "__main__":
    try:
        success = test_std_modifications()
        if success:
            print("\n✅ STD修改测试成功!")
        else:
            print("\n❌ STD修改测试失败!")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

# 标准差误差棒功能说明

## 概述

本项目已成功为 `train_MLP.PY` 添加了标准差误差棒的输出功能，提供更详细的统计分析和可视化。

## 新增功能

### 1. 带标准差误差棒的绘图函数

新增了 `plot_results_with_std_errorbars()` 函数，提供以下功能：

- **训练曲线误差棒**: 显示训练和验证损失的滑动窗口标准差
- **MAE曲线误差棒**: 显示验证MAE的滑动窗口标准差  
- **预测结果误差棒**: 按预测值分组显示真实值的标准差
- **误差分布统计**: 包含正态分布拟合和统计信息
- **详细误差分析**: 残差时间序列、Q-Q图、累积分布等

### 2. 增强的统计分析

- **置信区间**: 50%, 68%, 95%, 99% 的误差置信区间
- **正态性检验**: Shapiro-Wilk检验（如果scipy可用）
- **变异系数**: 误差标准差与MAE的比值
- **趋势分析**: 误差与预测值的相关性分析

### 3. 生成的图表文件

运行 `train_MLP.PY` 后会在 `./results/` 目录下生成：

1. `training_summary.png` - 基础训练总结图
2. `training_summary_with_std.png` - 带标准差误差棒的训练总结图
3. `detailed_error_analysis_with_std.png` - 详细误差分析图
4. `detailed_evaluation.png` - 详细评估图
5. `training_results.npz` - 包含详细统计信息的数据文件

## 使用方法

### 直接运行

```bash
cd benchmark_MLP/Bistatic_ISAC-main
python train_MLP.PY
```

### 测试功能

```bash
cd benchmark_MLP/Bistatic_ISAC-main
python test_std_errorbars.py
```

## 输出示例

### 控制台输出

```
================================================================================
📊 详细统计分析（包含标准差信息）
================================================================================
📈 误差统计:
   平均绝对误差 (MAE):        6.585951° ± 7.518296°
   均方根误差 (RMSE):         8.086748°
   最大误差:                  17.512156°
   误差标准差:                7.518296°
   误差变异系数 (CV):         1.142

📊 置信区间:
    50.0% 的误差 ≤ 5.2106°
    68.0% 的误差 ≤ 7.9808°
    95.0% 的误差 ≤ 14.3052°
    99.0% 的误差 ≤ 16.8708°

📋 正态性检验:
   Shapiro-Wilk 统计量:       0.991197
   p-value:                   0.999213
   正态性结论:                接受 (α=0.05)
================================================================================
```

### 图表特性

1. **误差棒显示**: 所有关键图表都包含标准差误差棒
2. **滑动窗口统计**: 使用可配置的窗口大小计算移动统计
3. **置信区间可视化**: 在累积分布图中显示多个置信水平
4. **正态性检验**: Q-Q图和统计检验结果
5. **趋势分析**: 误差与预测值的相关性分析

## 配置参数

在 `plot_results_with_std_errorbars()` 函数中可以调整：

- `window_size`: 滑动窗口大小（默认10）
- `n_bins`: 预测值分组数量（默认20）
- 置信水平列表（默认[0.5, 0.68, 0.95, 0.99]）

## 依赖库

- **必需**: numpy, torch, matplotlib, sklearn
- **可选**: scipy（用于高级统计功能，如果没有会使用近似方法）

## 兼容性

- 兼容原有的 `train_MLP.PY` 功能
- 自动检测scipy可用性，提供降级方案
- 支持CUDA和CPU训练

## 故障排除

如果遇到导入错误，请确保：

1. 所有依赖库已安装
2. 在正确的目录下运行脚本
3. Python环境配置正确

如果scipy不可用，程序会自动使用近似方法，不会影响核心功能。

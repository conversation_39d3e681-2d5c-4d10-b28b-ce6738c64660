<!DOCTYPE html>
<html lang="en">
  <head>
    <link rel="stylesheet" href="{{ prefix }}/_static/css/page.css" type="text/css">
    <link rel="stylesheet" href="{{ prefix }}/_static/css/boilerplate.css" type="text/css">
    <link rel="stylesheet" href="{{ prefix }}/_static/css/fbm.css" type="text/css">
    <link rel="stylesheet" href="{{ prefix }}/_static/css/mpl.css" type="text/css">
    <script src="{{ prefix }}/_static/js/mpl_tornado.js"></script>
    <script src="{{ prefix }}/js/mpl.js"></script>
    <script>
      function ready(fn) {
        if (document.readyState != "loading") {
          fn();
        } else {
          document.addEventListener("DOMContentLoaded", fn);
        }
      }

      ready(
        function () {
          var websocket_type = mpl.get_websocket_type();
          var uri = "{{ ws_uri }}" + {{ str(fig_id) }} + "/ws";
          if (window.location.protocol === 'https:') uri = uri.replace('ws:', 'wss:')
          var websocket = new websocket_type(uri);
          var fig = new mpl.figure(
              {{ str(fig_id) }}, websocket, mpl_ondownload,
              document.getElementById("figure"));
        }
      );
    </script>

    <title>matplotlib</title>
  </head>

  <body>
    <div id="mpl-warnings" class="mpl-warnings"></div>
    <div id="figure" style="margin: 10px 10px;"></div>
  </body>
</html>
